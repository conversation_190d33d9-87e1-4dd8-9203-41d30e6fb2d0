import React, { useState } from "react"
import { Button, Modal } from "@design-systems/apollo-ui"

export default function ModalPreview() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal
      </Button>
      <Modal
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal Title"
      >
        <div>
          Once upon a time, there was a forest where plenty of birds lived and
          built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
