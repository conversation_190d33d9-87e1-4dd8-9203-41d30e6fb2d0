import { Checkbox, MenuList, MenuOption } from "@/components"

const menuOptions = [
  { label: "Option 1" },
  { label: "Option 2" },
  { label: "Option 3", disabled: true },
  { label: "Option 4" },
  { label: "Option 5" },
  { label: "Option 6", disabled: true },
  { label: "Option 7", selected: true },
  { label: "Option 8" },
  { label: "Option 9" },
  { label: "Option 10" },
  { label: "Option 11" },
  { label: "Option 12" },
]

export default function MenuOptionDemo() {
  return (
    <div className="flex w-[256px] flex-row justify-center items-center">
      <MenuList>
        {menuOptions.map(({ label, selected, disabled }) => (
          <MenuOption
            key={label}
            label={label}
            startDecorator={<Checkbox disabled={disabled} checked={selected} />}
            selected={selected}
            disabled={disabled}
          />
        ))}
      </MenuList>
    </div>
  )
}
