import { UploadBox, useUploadMultipleFile } from "@/components"

export default function UploadBoxMultipleFileDemo() {
  const uploadFile = async (file: File): Promise<File> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(file)
      }, 3000)
    })
  }

  const uploadBoxProps = useUploadMultipleFile({
    uploadFileFn: uploadFile,
  })

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <UploadBox
        {...uploadBoxProps}
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        multiple
        maxFileSizeInBytes={24 * 1024 * 1024}
      />
    </div>
  )
}
