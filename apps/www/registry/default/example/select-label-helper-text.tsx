import React from "react"
import { Option, Select } from "@design-systems/apollo-ui"

export default function SelectWithLabelAndHelperText() {
  return (
    <Select
      label="Label"
      helperText="helper text"
      placeholder="Choose a character…"
      id="named-select"
      name="demo-select"
      defaultValue={10}
    >
      <Option value={10}>Default</Option>
      <Option value={20}>Error</Option>
      <Option value={30}>Thirty</Option>
    </Select>
  )
}
