import {
  Typography,
  UploadBox,
  useUploadMultipleFile,
  useUploadSingleFile,
} from "@/components"

export default function UploadBoxLoadingStateDemo() {
  const uploadFile = async (file: File): Promise<File> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(file)
      }, 3000)
    })
  }

  const singleFileUploadBoxProps = useUploadSingleFile({
    uploadFileFn: uploadFile,
    onDelete: () => {
      console.log("[DEBUG] event> delete file")
    },
    onCancelUpload: (file) => {
      console.log("[DEBUG] event> cancel upload:", file)
    },
  })

  const multipleFileUploadBoxProps = useUploadMultipleFile({
    uploadFileFn: uploadFile,
    onDelete: (fileIndex) => {
      console.log("[DEBUG] event> delete file index: ", fileIndex)
    },
    onCancelUpload: (file, fileIndex) => {
      console.log(
        "[DEBUG] event> cancel upload file index and file:",
        fileIndex,
        file
      )
    },
  })

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <Typography level="h4">Single File</Typography>
      <UploadBox
        {...singleFileUploadBoxProps}
        fullWidth
        label="File Upload: (Loading State)"
        allowedFilesExtension={["jpg", "png"]}
        required
        maxFileSizeInBytes={24 * 1024 * 1024}
      />
      <br />
      <Typography level="h4">Multiple File</Typography>
      <UploadBox
        {...multipleFileUploadBoxProps}
        fullWidth
        label="File Upload: (Loading State)"
        allowedFilesExtension={["jpg", "png"]}
        required
        maxFileSizeInBytes={24 * 1024 * 1024}
        multiple
      />
    </div>
  )
}
