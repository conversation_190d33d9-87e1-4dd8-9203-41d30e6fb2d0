import { useState } from "react"
import { Sidebar, Typography, type SidebarMenu } from "@/components"
import { AreaChart, Home, Setting, User } from "@design-systems/apollo-icons"

import { Icons } from "@/components/icons"
import { MenuItem, MenuItemGroup } from "@/components/MenuItem"

export default function SidebarDemo() {
  const [selectedMenu, setSelectedMenu] = useState<string | number>("overview")
  const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>([
    "analytics",
  ])

  const handleExpandStateChange = (key: string | number, expanded: boolean) => {
    setExpandedMenuKeys((prev) =>
      expanded
        ? [...prev, key as string]
        : prev.filter((prevKey) => prevKey !== key)
    )
  }

  return (
    <div className="w-full h-[500px] self-stretch bg-slate-300 border border-border-default overflow-auto">
      <Sidebar
        header={
          <div className="flex flex-row justify-start items-center gap-2 p-4">
            <Typography level="h5">Apollo</Typography>
          </div>
        }
        menus={menuConfig}
        selectedMenuKey={selectedMenu}
        onSelectMenu={setSelectedMenu}
        expandedMenuKeys={expandedMenuKeys}
        onExpandedChange={handleExpandStateChange}
        footer={
          <MenuItemGroup icon={<User />} label="Profile" selected>
            <MenuItem label="My Products" subItem />
          </MenuItemGroup>
        }
        onLogOut={() => {
          alert("Logout!")
        }}
      />
    </div>
  )
}

const menuConfig: SidebarMenu[] = [
  {
    key: "dashboard",
    label: "Dashboard",
    items: [
      { key: "home", label: "Home", icon: <AreaChart /> },
      {
        key: "analytics",
        label: "Analytics",
        icon: <AreaChart />,
        children: [
          { key: "overview", icon: <AreaChart />, label: "Overview" },
          { key: "reports", icon: <AreaChart />, label: "Reports" },
        ],
      },
    ],
  },
  { key: "settings", label: "Settings", icon: <AreaChart /> },
  {
    key: "profile",
    label: "Profile",
    icon: <AreaChart />,
    children: [
      { key: "account", icon: <AreaChart />, label: "Account Settings" },
      { key: "security", icon: <AreaChart />, label: "Security" },
    ],
  },
]
