import { useState } from "react"
import { Radio, RadioGroup } from "@design-systems/apollo-ui"

export default function RadioGroupDemo() {
  const [selectedOptions, setSelectedOptions] = useState<number>(1)

  return (
    <div className="flex flex-col gap-3">
      <RadioGroup value={selectedOptions} onChange={setSelectedOptions}>
        <Radio label="Option1" value={1} />
        <Radio label="Option2" value={2} />
        <Radio label="Disabled Option" disabled value={3} />
      </RadioGroup>
    </div>
  )
}
