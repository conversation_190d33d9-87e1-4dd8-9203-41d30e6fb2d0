import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithDisabledButtons() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Disabled Buttons
      </Button>
      <Modal
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        disabledOkButton={true}
        disabledCancelButton={true}
        okButtonText="OK"
        cancelButtonText="Cancel"
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Disabled Buttons"
      >
        <div>
          This modal includes disabled OK, Cancel, and Delete buttons. Once upon
          a time, there was a forest where plenty of birds lived and built their
          nests on the trees.
        </div>
      </Modal>
    </>
  )
}
