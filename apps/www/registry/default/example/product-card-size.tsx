import Image from "next/image"
import { Button, ProductCard, Typography } from "@design-systems/apollo-ui"

export default function ProductCardDefault() {
  return (
    <div className="p-4 gap-2 w-full flex flex-col justify-start items-start">
      <Typography>Fix (Default: 160px)</Typography>
      <div className="flex flex-row flex-wrap justify-start items-start w-full bg-surface-static-default1">
        <ProductCard
          ImageComponent={Image}
          imageProps={{ layout: "fill" }}
          imageSrc="https://picsum.photos/300/300?random=1"
          size="160px"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ layout: "fill" }}
          imageSrc="https://picsum.photos/300/300?random=2"
          size="160px"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ layout: "fill" }}
          imageSrc="https://picsum.photos/300/300?random=3"
          size="160px"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ layout: "fill" }}
          imageSrc="https://picsum.photos/300/300?random=4"
          size="160px"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
      </div>
      <Typography>Fill</Typography>
      <div className="w-full grid grid-cols-[repeat(auto-fill,minmax(100px,180px))] justify-center gap-2 bg-surface-static-default1">
        <ProductCard
          ImageComponent={Image}
          imageProps={{ height: 160, width: 160 }}
          imageSrc="https://picsum.photos/300/300?random=1"
          size="fill"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ height: 160, width: 160 }}
          imageSrc="https://picsum.photos/300/300?random=2"
          size="fill"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ height: 160, width: 160 }}
          imageSrc="https://picsum.photos/300/300?random=3"
          size="fill"
          title="ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
        <ProductCard
          ImageComponent={Image}
          imageProps={{ height: 160, width: 160 }}
          imageSrc="https://picsum.photos/300/300?random=4"
          size="fill"
          title="ผ้าอ้อมสำเร็จรูป Moby ผ้าอ้อมสำเร็จรูป Moby"
          body={
            <Typography level="caption" className="text-content-danger-default">
              หมดอายุ 12/07/2567 (23:59)
            </Typography>
          }
          footer={
            <Button fullWidth variant="solid" color="primary">
              เก็บคูปอง
            </Button>
          }
        />
      </div>
    </div>
  )
}
