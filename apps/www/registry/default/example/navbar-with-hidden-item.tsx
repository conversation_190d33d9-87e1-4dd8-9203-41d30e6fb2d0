import React, { useState } from "react"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
} from "@design-systems/apollo-icons"
import { Button, NavBar } from "@design-systems/apollo-ui"

export default function NavBarWithHiddenItems() {
  const [isShowAllMenu, setIsShowAllMenu] = useState(true)
  return (
    <div className="w-full flex flex-col justify-center items-center gap-2">
      <Button onClick={() => setIsShowAllMenu((prev) => !prev)}>
        {isShowAllMenu ? "HIDE" : "SHOW"}
      </Button>
      <NavBar
        menu={[
          {
            label: "Home",
            icon: <Home />,
          },
          {
            label: "Shop",
            icon: <Shop />,
            hidden: !isShowAllMenu,
          },
          {
            label: "My Shopping",
            icon: <Shopping />,
            hidden: !isShowAllMenu,
          },
          {
            label: "Tasks",
            icon: <Solution />,
          },
          {
            label: "Profile",
            icon: <User />,
          },
        ]}
      />
    </div>
  )
}
