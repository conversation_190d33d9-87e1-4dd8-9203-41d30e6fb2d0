import { useState } from "react"
import { Typography, UploadBox } from "@/components"

export default function UploadBoxFileLimitDemo() {
  const [files, setFiles] = useState<File[] | null>()

  const handleDeleteMultipleFile = (deleteFileIndex: number) => {
    setFiles((prev) => prev?.filter((_, index) => index !== deleteFileIndex))
  }

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <Typography level="h3">Allowed only 2 files</Typography>
      <UploadBox
        fullWidth
        label="File Upload: "
        required
        multiple
        onDelete={handleDeleteMultipleFile}
        onUpload={setFiles}
        value={files}
        fileLimit={2}
      />
    </div>
  )
}
