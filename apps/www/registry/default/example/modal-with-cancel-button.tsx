import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithCancelButton() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Cancel Button
      </Button>
      <Modal
        onCancel={() => {
          console.log("[Click Event]: Cancel button clicked")
        }}
        cancelButtonText="Cancel"
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Cancel Button"
      >
        <div>
          This modal includes a Cancel button. Once upon a time, there was a
          forest where plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
