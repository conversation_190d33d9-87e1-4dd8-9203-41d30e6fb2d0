import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithCloseOnBackdropClick() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Close on Backdrop Click
      </Button>
      <Modal
        closeAfterClickBackdrop={true}
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Close on Backdrop Click"
      >
        <div>
          Click on the backdrop to close this modal. Once upon a time, there was
          a forest where plenty of birds lived and built their nests on the
          trees.
        </div>
      </Modal>
    </>
  )
}
