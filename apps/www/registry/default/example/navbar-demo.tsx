import React, { useCallback, useState } from "react"
import { Icon, NavBar } from "@design-systems/apollo-ui"
import { NavBarMenuItem } from "@design-systems/apollo-ui/src/components/NavBar/NavBarProps"

export default function NavBarDemo() {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const handleChange = useCallback((index: number, menu: NavBarMenuItem) => {
    console.log("[Click] Menu: ", menu)
    setSelectedIndex(index)
  }, [])

  return (
    <NavBar
      activeIndex={selectedIndex}
      onChange={handleChange}
      menu={[
        {
          label: "Label",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Label",
          icon: <HomeIcon />,
          badge: "99+",
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Lorem ipsum dolor sit amet ",
          icon: <HomeIcon />,
          badge: "1",
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Label",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Label",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
        },
      ]}
    />
  )
}

function HomeIcon() {
  return (
    <Icon viewBox="1 2 32 32" color="primary">
      <path
        d="M25.5397 29.0932H19.4545C18.641 29.0932 17.9804 28.4325 17.9804 27.619V21.1945H14.2142V27.619C14.2142 28.4325 13.5535 29.0932 12.74 29.0932H6.65479C5.88402 29.0932 5.18067 28.4819 5.18067 27.8123V15.3834H2.85937C2.04141 15.3834 1.3493 14.88 1.09538 14.1025C0.841449 13.325 1.10436 12.5115 1.76727 12.0284L15.1332 2.28482C15.6523 1.90506 16.3512 1.90506 16.8703 2.28482L19.9309 4.51623C20.1511 3.97916 20.6792 3.59715 21.2971 3.59715H25.5397C26.3532 3.59715 27.0138 4.25781 27.0138 5.07127V9.68015L30.234 12.0284C30.8946 12.5093 31.1576 13.325 30.9059 14.1025C30.652 14.88 29.9598 15.3834 29.1419 15.3834H27.0138V27.619C27.0138 28.4325 26.3532 29.0932 25.5397 29.0932ZM19.7781 27.2955H25.2161V14.8171C25.2161 14.1362 25.7689 13.5834 26.4498 13.5834H29.1419C29.1688 13.5834 29.1823 13.5834 29.1958 13.5452C29.2093 13.507 29.1958 13.498 29.1756 13.4823L25.8229 11.0374C25.4431 10.761 25.2161 10.3161 25.2161 9.84644V5.39711H21.5848C21.4904 5.82182 21.2117 6.18585 20.814 6.38809C20.3174 6.63977 19.7286 6.59483 19.277 6.2645L16.0006 3.8758L2.82566 13.4823C2.80319 13.498 2.79196 13.507 2.80544 13.5452C2.81893 13.5834 2.83241 13.5834 2.85937 13.5834H5.50426C6.23682 13.5834 6.97838 14.1564 6.97838 15.2508V27.2955H12.4165V20.8709C12.4165 20.0574 13.0771 19.3968 13.8906 19.3968H18.3039C19.1174 19.3968 19.7781 20.0574 19.7781 20.8709V27.2955Z"
        fill="currentColor"
      />
    </Icon>
  )
}

function HomeFilledIcon() {
  return (
    <Icon viewBox="0 0 32 32" color="primary">
      <path
        d="M29.2327 10.0284L26.0126 7.68015V3.07127C26.0126 2.25781 25.3519 1.59715 24.5385 1.59715H20.2959C19.6801 1.59715 19.1498 1.97692 18.9296 2.51623L15.869 0.284825C15.3499 -0.0949415 14.651 -0.0949415 14.132 0.284825L0.765995 10.0284C0.105337 10.5093 -0.157576 11.325 0.0941036 12.1025C0.345783 12.88 1.04014 13.3834 1.8581 13.3834H4.1794V25.8123C4.1794 26.4842 4.88275 27.0932 5.65352 27.0932H11.7388C12.5522 27.0932 13.2129 26.4325 13.2129 25.619V19.1945H16.9791V25.619C16.9791 26.4325 17.6397 27.0932 18.4532 27.0932H24.5385C25.3519 27.0932 26.0126 26.4325 26.0126 25.619V13.3834H28.1406C28.9586 13.3834 29.6507 12.88 29.9046 12.1025C30.1586 11.325 29.8956 10.5115 29.2327 10.0284Z"
        fill="currentColor"
      />
    </Icon>
  )
}
