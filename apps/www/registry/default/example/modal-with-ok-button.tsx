import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithOkButton() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with OK Button
      </Button>
      <Modal
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        okButtonText="Confirm"
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with OK Button"
      >
        <div>
          This modal includes an OK button. Once upon a time, there was a forest
          where plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
