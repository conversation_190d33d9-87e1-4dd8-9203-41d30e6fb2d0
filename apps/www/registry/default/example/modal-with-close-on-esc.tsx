import React, { useState } from "react"
import { Button, Modal } from "@design-systems/apollo-ui"

export default function ModalWithCloseOnESC() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Close on ESC
      </Button>
      <Modal
        closeAfterPressEsc={true}
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Close on ESC"
      >
        <div>
          Press ESC to close this modal. Once upon a time, there was a forest
          where plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
