import React, { useCallback, useState } from "react"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
} from "@design-systems/apollo-icons"
import { NavBar } from "@design-systems/apollo-ui"
import { NavBarMenuItem } from "@design-systems/apollo-ui/src/components/NavBar/NavBarProps"

type ExtendedNavBarMenuItem = {
  customProperty?: string
} & NavBarMenuItem

export default function NavBarWithAdditionalProperty() {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const handleChange = useCallback(
    (index: number, menu: ExtendedNavBarMenuItem) => {
      console.log("[Click] Menu: ", menu)
      setSelectedIndex(index)
    },
    []
  )

  return (
    <NavBar
      activeIndex={selectedIndex}
      onChange={handleChange}
      menu={[
        {
          label: "Home",
          icon: <Home />,
          customProperty: "customValue1",
        },
        {
          label: "Shop",
          icon: <Shop />,
          customProperty: "customValue2",
        },
        {
          label: "My Shopping",
          icon: <Shopping />,
          customProperty: "customValue3",
        },
        {
          label: "Tasks",
          icon: <Solution />,
          customProperty: "customValue4",
        },
        {
          label: "Profile",
          icon: <User />,
          customProperty: "customValue5",
        },
      ]}
    />
  )
}
