import { useEffect, useState } from "react"
import { UploadBox, UploadBoxFileState } from "@/components"

import { UploadBoxBaseFileType } from "@/components/UploadBox/UploadBoxProps"

export default function UploadBoxDemo() {
  const [file, setFile] = useState<File | null>()
  const [fileState, setFileState] = useState<UploadBoxFileState>()

  const handleDeleteSingleFile = (
    file: UploadBoxBaseFileType,
    index: number
  ) => {
    console.log("[DEBUG] deleting file index:", index, file)
    setFile(null)
  }

  const handleUploadFiles = (file: File) => {
    setFile(file)
    setFileState({
      key: "file",
      uploading: true,
    })
  }

  useEffect(() => {
    if (fileState?.uploading) {
      setTimeout(() => {
        setFileState({ key: "file", uploading: false })
      }, 5000)
    }
  }, [fileState?.uploading])

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <UploadBox
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        value={file}
        fileState={fileState}
        onDelete={() => setFile(null)}
        onCancelUpload={handleDeleteSingleFile}
        maxFileSizeInBytes={24 * 1024 * 1024}
        onUpload={handleUploadFiles}
      />
    </div>
  )
}
