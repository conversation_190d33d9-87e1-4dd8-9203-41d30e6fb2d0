import { MenuList, MenuOption } from "@/components"

const menuOptions = [
  { label: "Option 1", onClick: () => console.log("Option 1") },
  { label: "Option 2", onClick: () => console.log("Option 2") },
  { label: "Option 3", onClick: () => console.log("Option 3"), disabled: true },
  { label: "Option 4", onClick: () => console.log("Option 4") },
  { label: "Option 5", onClick: () => console.log("Option 5") },
  { label: "Option 6", onClick: () => console.log("Option 6"), disabled: true },
  { label: "Option 7", onClick: () => console.log("Option 7"), selected: true },
  { label: "Option 8", onClick: () => console.log("Option 8") },
  { label: "Option 9", onClick: () => console.log("Option 9") },
  { label: "Option 10", onClick: () => console.log("Option 10") },
  { label: "Option 11", onClick: () => console.log("Option 11") },
  { label: "Option 12", onClick: () => console.log("Option 12") },
]

export default function MenuOptionDemo() {
  return (
    <div className="flex w-[256px] flex-row justify-center items-center">
      <MenuList>
        {menuOptions.map(({ label, onClick, selected, disabled }) => (
          <MenuOption
            key={label}
            label={label}
            onClick={onClick}
            selected={selected}
            disabled={disabled}
          />
        ))}
      </MenuList>
    </div>
  )
}
