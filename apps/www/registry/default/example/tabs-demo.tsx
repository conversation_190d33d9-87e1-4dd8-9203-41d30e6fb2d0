import { Tab, <PERSON>bP<PERSON><PERSON>, <PERSON>bs, <PERSON>bsList } from "@design-systems/apollo-ui"

export default function TabsDemo() {
  return (
    <div className="w-full">
      <Tabs defaultValue={0}>
        <TabsList variant="scrollable" scrollButtons>
          <Tab value={0}>Tab 1</Tab>
          <Tab value={1}>Tab 2</Tab>
          <Tab value={2}>Tab 3</Tab>
          <Tab value={3}>Tab 4</Tab>
        </TabsList>

        <TabPanel value={0}>Page for Tab 1</TabPanel>
        <TabPanel value={1}>Page for Tab 2</TabPanel>
        <TabPanel value={2}>Page for Tab 3</TabPanel>
        <TabPanel value={3}>Page for Tab 4</TabPanel>
      </Tabs>
      <Tabs defaultValue={0}>
        <TabsList variant="scrollable" scrollButtons>
          <Tab value={0}>
            Lorem ipsum dolor sit amet consectetur adipiscing elit Lorem ipsum
            dolor sit amet consectetur adipiscing elitsed do
          </Tab>
          <Tab value={1}>
            Lorem ipsum dolor sit amet consectetur adipiscing elit Lorem ipsum
            dolor sit amet consectetur adipiscing elitsed do
          </Tab>
          <Tab value={2}>
            Lorem ipsum dolor sit amet consectetur adipiscing elit Lorem ipsum
            dolor sit amet consectetur adipiscing elitsed do
          </Tab>
          <Tab value={3}>
            Lorem ipsum dolor sit amet consectetur adipiscing elit Lorem ipsum
            dolor sit amet consectetur adipiscing elitsed do
          </Tab>
        </TabsList>

        <TabPanel value={0}>Page for Tab 1</TabPanel>
        <TabPanel value={1}>Page for Tab 2</TabPanel>
        <TabPanel value={2}>Page for Tab 3</TabPanel>
        <TabPanel value={3}>Page for Tab 4</TabPanel>
      </Tabs>
    </div>
  )
}
