import { useState } from "react"
import { Option, Select, Typography } from "@design-systems/apollo-ui"

export default function SelectControlled() {
  const [value, setValue] = useState<number | null>(10)
  return (
    <div className="w-80 space-y-2">
      <Select
        id="named-select"
        name="demo-select"
        value={value}
        onChange={(_, newValue) => setValue(newValue)}
      >
        <Option value={10}>Ten</Option>
        <Option value={20}>Twenty</Option>
        <Option value={30}>Thirty</Option>
      </Select>
      <Typography level="body-2">Selected value: {value}</Typography>
    </div>
  )
}
