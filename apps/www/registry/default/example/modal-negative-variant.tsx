import React, { useState } from "react"
import { Button, NegativeModal } from "@design-systems/apollo-ui"

export default function ModalWithDeleteButton() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Negative Modal
      </Button>
      <NegativeModal
        onConfirm={() => {
          console.log("Confirm Button is Clicked")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Negative Modal"
      >
        <div>
          This modal is specifically used for negative action alerts, such as
          Delete or Cancel operations.
        </div>
      </NegativeModal>
    </>
  )
}
