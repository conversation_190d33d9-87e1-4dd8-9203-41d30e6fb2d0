import {
  Button,
  ToastProvider,
  Typography,
  useToast,
} from "@design-systems/apollo-ui"

function ToastWithHook() {
  const { showSuccessToast, showErrorToast, showToast } = useToast()

  return (
    <div className="flex flex-col gap-2">
      <Button
        onClick={() =>
          showSuccessToast({
            title: "Sucess",
            endDecorator: (
              <Button variant="outline" size="md">
                Detail
              </Button>
            ),
            description: "Lorem ipsum ipsum",
          })
        }
      >
        Show Success
      </Button>
      <Button
        onClick={() =>
          showErrorToast({
            title: "Error",
            description: "Lorem ipsum ipsum",
          })
        }
      >
        Show Error
      </Button>
      <Button
        onClick={() =>
          showToast({
            title: <Typography level="h1">BIG TITLE</Typography>,
            description: (
              <div className="flex flex-col gap-2 justify-start items-start p-2">
                <Typography level="body-2">Item #</Typography>
                <Typography level="body-2">Item #</Typography>
                <Typography level="body-2">Item #</Typography>
                <Typography level="body-2">Item #</Typography>
                <Typography level="body-2">Item #</Typography>
              </div>
            ),
            variant: "error",
          })
        }
      >
        Show Custom Toast
      </Button>
    </div>
  )
}

export default function ProviderWrapper() {
  return (
    <ToastProvider>
      <ToastWithHook />
    </ToastProvider>
  )
}
