import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithDeleteButton() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Delete Button
      </Button>
      <Modal
        onDelete={() => {
          console.log("[Click Event]: Delete button clicked")
        }}
        deleteButtonText="Delete"
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Delete Button"
      >
        <div>
          This modal includes a Delete button.
          <br /> <b>**Note:**</b> When the Delete button is displayed, the OK
          and Cancel buttons will be hidden. Once upon a time, there was a
          forest where plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
