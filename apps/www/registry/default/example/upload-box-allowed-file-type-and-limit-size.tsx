import { useState } from "react"
import { Typography, UploadBox } from "@/components"

export default function UploadBoxAllowedFileTypeAndLimitSizeDemo() {
  const [file, setFile] = useState<File | null>()

  const handleDeleteSingleFile = () => {
    setFile(null)
  }

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <Typography level="h3">Allowed jpg and png only</Typography>
      <UploadBox
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        value={file}
        onDelete={handleDeleteSingleFile}
        maxFileSizeInBytes={24 * 1024 * 1024}
        onUpload={setFile}
      />
      <Typography level="h3">Limit File Size (100kb)</Typography>
      <UploadBox
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        value={file}
        onDelete={handleDeleteSingleFile}
        maxFileSizeInBytes={100 * 1024}
        onUpload={setFile}
      />
    </div>
  )
}
