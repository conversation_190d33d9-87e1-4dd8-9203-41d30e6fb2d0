import { useState } from "react"
import { Sidebar, Typography, type SidebarMenu } from "@/components"
import { AreaChart, Home, Setting, User } from "@design-systems/apollo-icons"

import { Icons } from "@/components/icons"
import { MenuItem, MenuItemGroup } from "@/components/MenuItem"

export default function SidebarDemo() {
  const [selectedMenu, setSelectedMenu] = useState<string | number>("overview")
  const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>([
    "analytics",
  ])

  const handleExpandStateChange = (key: string | number, expanded: boolean) => {
    setExpandedMenuKeys((prev) =>
      expanded
        ? [...prev, key as string]
        : prev.filter((prevKey) => prevKey !== key)
    )
  }

  return (
    <div className="w-full h-[500px] self-stretch bg-slate-300 border border-border-default overflow-auto">
      <Sidebar
        header={
          <div className="flex flex-row justify-start items-center gap-2 p-4">
            <Typography level="h5">Apollo</Typography>
          </div>
        }
        menus={menuConfig}
        selectedMenuKey={selectedMenu}
        onSelectMenu={setSelectedMenu}
        expandedMenuKeys={expandedMenuKeys}
        onExpandedChange={handleExpandStateChange}
        footer={
          <MenuItemGroup icon={<User />} label="Profile" selected>
            <MenuItem label="My Products" subItem />
          </MenuItemGroup>
        }
        onLogOut={() => {
          alert("Logout!")
        }}
      />
    </div>
  )
}

const menuConfig: SidebarMenu[] = [
  {
    key: "dashboard",
    label: "Dashboard",
    items: [
      {
        key: "home",
        label: "Home",
        icon: <Home />,
        href: "https://www.google.com",
      },
      {
        key: "analytics",
        label: "Analytics",
        icon: <AreaChart />,
        children: [
          {
            key: "overview",
            label: "Overview",
            icon: <AreaChart />,
            onClick() {
              alert("Overview!")
            },
          },
          { key: "reports", label: "Reports", icon: <AreaChart /> },
        ],
      },
      {
        key: "analytics2",
        label: "Analytics #2",
        icon: <AreaChart />,
        children: [
          { key: "overview2", label: "Overview", icon: <AreaChart /> },
          { key: "reports2", label: "Reports", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    items: [
      { key: "projects", label: "Projects", icon: <AreaChart /> },
      { key: "teams", label: "Teams", icon: <AreaChart /> },
      {
        key: "resources",
        label: "Resources",
        icon: <AreaChart />,
        children: [
          { key: "assets", label: "Assets", icon: <AreaChart /> },
          { key: "inventory", label: "Inventory", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "settings",
    label: "Settings",
    items: [
      { key: "profile", label: "Profile", icon: <Home /> },
      { key: "preferences", label: "Preferences", icon: <AreaChart /> },
      {
        key: "notifications",
        label: "Notifications",
        icon: <AreaChart />,
        children: [
          { key: "email", label: "Email", icon: <AreaChart /> },
          { key: "sms", label: "SMS", icon: <AreaChart /> },
        ],
      },
    ],
  },
  {
    key: "support",
    label: "Support",
    items: [
      { key: "faq", label: "FAQ", icon: <Home /> },
      { key: "contact", label: "Contact Us", icon: <AreaChart /> },
      {
        key: "feedback",
        label: "Feedback",
        icon: <AreaChart />,
        children: [
          { key: "suggestions", label: "Suggestions", icon: <AreaChart /> },
          { key: "complaints", label: "Complaints", icon: <AreaChart /> },
        ],
      },
    ],
  },
]
