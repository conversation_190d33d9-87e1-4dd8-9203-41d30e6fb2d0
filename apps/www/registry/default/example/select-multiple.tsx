import React, { useState } from "react"
import { Option, Select, Typography } from "@design-systems/apollo-ui"

export default function MultipleSelect() {
  const [value, setValue] = useState<number[]>([10, 20])
  return (
    <Select
      label="Numbers"
      helperText={`Selected value: ${value.join(",")}`}
      multiple
      id="named-select"
      name="demo-select"
      value={value}
      onChange={(_, newValue) => setValue(newValue)}
    >
      <Option value={10}>Ten</Option>
      <Option value={20}>Twenty</Option>
      <Option value={30}>Thirty</Option>
      <Option value={40}>Fourthy</Option>
      <Option value={50}>Fifty</Option>
      <Option value={60}>Sixty</Option>
      <Option value={70}>Seventy</Option>
      <Option value={80}>Eighty</Option>
      <Option value={90}>Ninety</Option>
      <Option value={100}>Hundred</Option>
    </Select>
  )
}
