import React, { useState } from "react"
import { Button, Modal } from "@design-systems/apollo-ui"

export default function ModalFullSize() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Full-size Modal
      </Button>
      <Modal
        size="full"
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Full-size Modal Title"
      >
        <div>
          This is a full-size modal. Once upon a time, there was a forest where
          plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
