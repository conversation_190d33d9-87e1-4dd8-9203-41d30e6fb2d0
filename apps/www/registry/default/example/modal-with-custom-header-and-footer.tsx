import React, { useState } from "react"
import { But<PERSON>, Modal } from "@design-systems/apollo-ui"

export default function ModalWithCustomHeaderAndFooter() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Custom Header and Footer
      </Button>
      <Modal
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header={<h2>Custom Header</h2>}
        footer={
          <button onClick={() => console.log("Footer button clicked")}>
            Footer Button
          </button>
        }
      >
        <div>
          This modal has a custom header and footer. Once upon a time, there was
          a forest where plenty of birds lived and built their nests on the
          trees.
        </div>
      </Modal>
    </>
  )
}
