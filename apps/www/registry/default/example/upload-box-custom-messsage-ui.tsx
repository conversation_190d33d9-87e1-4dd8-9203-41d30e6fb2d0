import { useState } from "react"
import { Typography, UploadBox } from "@/components"

export default function UploadBoxCustomMessageUI() {
  const [file, setFile] = useState<File | null>()

  const handleDeleteSingleFile = () => {
    setFile(null)
  }

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <UploadBox
        fullWidth
        label="File Upload (Custom Description / Error component)"
        allowedFilesExtension={["jpg", "png"]}
        required
        renderErrorMessage={(state) => (
          <div className="flex flex-row gap-2">
            Error Message:{" "}
            {state.errors?.map((error) => error.message).join(",")}
          </div>
        )}
        renderDescription={(state) => (
          <div className="flex flex-col gap-2">
            <Typography>Custom Descriotion ✨</Typography>
            <Typography>- Lorem Ipsum</Typography>
            <Typography>- Lorem Ipsum</Typography>
            ---
            <Typography>Error Messages</Typography>
            {state.errors?.map((error) => (
              <Typography key={error.code} level="caption">
                {error.message}
              </Typography>
            ))}
          </div>
        )}
        value={file}
        onDelete={handleDeleteSingleFile}
        maxFileSizeInBytes={0.001 * 1024 * 1024}
        onUpload={setFile}
      />
    </div>
  )
}
