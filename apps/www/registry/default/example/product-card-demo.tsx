import Image from "next/image"
import {
  <PERSON><PERSON>,
  Chip,
  ProductCard,
  Typography,
} from "@design-systems/apollo-ui"

export default function ProductCardDemo() {
  return (
    <div className="w-full grid grid-cols-[repeat(auto-fill,minmax(160px,180px))] justify-center gap-2">
      <ProductCard
        title="ลูกชิ้น y&ฤ ผ้าอ้อมสำเร็จรูป Moby ชนิดเทป (S) 40ชิ้น เหลือ 105บ."
        extra={
          <div className="self-stretch flex flex-row justify-center items-center">
            <Chip label="1 แถม 1" color="primary" />
          </div>
        }
        ImageComponent={Image}
        imageProps={{ layout: "fill" }}
        imageSrc="https://picsum.photos/300/300?random=2"
        imageOverlay="หมดแล้วครับ"
        body={
          <Typography level="caption" className="text-content-danger-default">
            หมดอายุ 12/07/2567 (23:59)
          </Typography>
        }
        onClick={() => {
          console.log("[Event] Click Card")
        }}
        footer={
          <Button
            fullWidth
            variant="solid"
            color="primary"
            onClick={(event) => {
              event.stopPropagation()
              console.log("[Event] Click Button on the card")
            }}
          >
            เก็บคูปอง
          </Button>
        }
      />
      <ProductCard
        title="useable เก็บไม่ได้ เพราะ สิทธิ์ครบแล้ว ทั้งหมด ทั้งหมด"
        ImageComponent={Image}
        imageProps={{ layout: "fill" }}
        imageSrc="https://picsum.photos/300/300?random=2"
        imageOverlay="หมดแล้วครับ"
        body={
          <Typography level="caption" className="text-content-danger-default">
            หมดอายุ 12/07/2567 (23:59)
          </Typography>
        }
        footer={
          <Button fullWidth disabled variant="solid" color="primary">
            เก็บคูปอง
          </Button>
        }
      />
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby"
        imageSrc="https://picsum.photos/200/300?random=1"
        body={
          <Typography level="caption" className="text-content-danger-default">
            หมดอายุ 12/07/2567 (23:59)
          </Typography>
        }
        footer={
          <Button variant="solid" color="primary">
            เก็บคูปอง
          </Button>
        }
      />
      <ProductCard
        title="ผ้าอ้อมสำเร็จรูป Moby"
        body={
          <Typography level="caption" className="text-content-danger-default">
            หมดอายุ 12/07/2567 (23:59)
          </Typography>
        }
        footer={
          <Button fullWidth variant="solid" color="primary">
            เก็บคูปอง
          </Button>
        }
      />
    </div>
  )
}
