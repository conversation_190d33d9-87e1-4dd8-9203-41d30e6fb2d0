import React, { useCallback, useState } from "react"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
} from "@design-systems/apollo-icons"
import { Icon, NavBar } from "@design-systems/apollo-ui"
import { NavBarMenuItem } from "@design-systems/apollo-ui/src/components/NavBar/NavBarProps"

export default function NavBarWithAdditionalIcons() {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const handleChange = useCallback((index: number, menu: NavBarMenuItem) => {
    console.log("[Click] Menu: ", menu)
    setSelectedIndex(index)
  }, [])

  return (
    <NavBar
      activeIndex={selectedIndex}
      onChange={handleChange}
      menu={[
        {
          label: "Home",
          icon: <HomeIcon />,
          activeIcon: <HomeFilledIcon />,
        },
        {
          label: "Discout",
          icon: <CouponIcon />,
          activeIcon: <CouponFilledIcon />,
        },
      ]}
    />
  )
}

function HomeIcon() {
  return (
    <Icon viewBox="1 2 32 32" color="primary">
      <path
        d="M25.5397 29.0932H19.4545C18.641 29.0932 17.9804 28.4325 17.9804 27.619V21.1945H14.2142V27.619C14.2142 28.4325 13.5535 29.0932 12.74 29.0932H6.65479C5.88402 29.0932 5.18067 28.4819 5.18067 27.8123V15.3834H2.85937C2.04141 15.3834 1.3493 14.88 1.09538 14.1025C0.841449 13.325 1.10436 12.5115 1.76727 12.0284L15.1332 2.28482C15.6523 1.90506 16.3512 1.90506 16.8703 2.28482L19.9309 4.51623C20.1511 3.97916 20.6792 3.59715 21.2971 3.59715H25.5397C26.3532 3.59715 27.0138 4.25781 27.0138 5.07127V9.68015L30.234 12.0284C30.8946 12.5093 31.1576 13.325 30.9059 14.1025C30.652 14.88 29.9598 15.3834 29.1419 15.3834H27.0138V27.619C27.0138 28.4325 26.3532 29.0932 25.5397 29.0932ZM19.7781 27.2955H25.2161V14.8171C25.2161 14.1362 25.7689 13.5834 26.4498 13.5834H29.1419C29.1688 13.5834 29.1823 13.5834 29.1958 13.5452C29.2093 13.507 29.1958 13.498 29.1756 13.4823L25.8229 11.0374C25.4431 10.761 25.2161 10.3161 25.2161 9.84644V5.39711H21.5848C21.4904 5.82182 21.2117 6.18585 20.814 6.38809C20.3174 6.63977 19.7286 6.59483 19.277 6.2645L16.0006 3.8758L2.82566 13.4823C2.80319 13.498 2.79196 13.507 2.80544 13.5452C2.81893 13.5834 2.83241 13.5834 2.85937 13.5834H5.50426C6.23682 13.5834 6.97838 14.1564 6.97838 15.2508V27.2955H12.4165V20.8709C12.4165 20.0574 13.0771 19.3968 13.8906 19.3968H18.3039C19.1174 19.3968 19.7781 20.0574 19.7781 20.8709V27.2955Z"
        fill="currentColor"
      />
    </Icon>
  )
}

function HomeFilledIcon() {
  return (
    <Icon viewBox="0 0 32 32" color="primary">
      <path
        d="M29.2327 10.0284L26.0126 7.68015V3.07127C26.0126 2.25781 25.3519 1.59715 24.5385 1.59715H20.2959C19.6801 1.59715 19.1498 1.97692 18.9296 2.51623L15.869 0.284825C15.3499 -0.0949415 14.651 -0.0949415 14.132 0.284825L0.765995 10.0284C0.105337 10.5093 -0.157576 11.325 0.0941036 12.1025C0.345783 12.88 1.04014 13.3834 1.8581 13.3834H4.1794V25.8123C4.1794 26.4842 4.88275 27.0932 5.65352 27.0932H11.7388C12.5522 27.0932 13.2129 26.4325 13.2129 25.619V19.1945H16.9791V25.619C16.9791 26.4325 17.6397 27.0932 18.4532 27.0932H24.5385C25.3519 27.0932 26.0126 26.4325 26.0126 25.619V13.3834H28.1406C28.9586 13.3834 29.6507 12.88 29.9046 12.1025C30.1586 11.325 29.8956 10.5115 29.2327 10.0284Z"
        fill="currentColor"
      />
    </Icon>
  )
}

function CouponIcon() {
  return (
    <Icon viewBox="0 0 32 32" color="primary">
      <path
        d="M27.5625 28.2286H3.93751C2.18036 28.2286 0.75 26.7982 0.75 25.0411V21.7545L1.79197 21.725C3.83572 21.6688 5.4375 20.0241 5.4375 17.9804C5.4375 15.9366 3.83572 14.292 1.79197 14.2357L0.75 14.2063V11.1875C0.75 9.43036 2.18036 8 3.93751 8H27.5625C29.3197 8 30.75 9.43036 30.75 11.1875V14.2679L29.6464 14.2357C29.6116 14.2357 29.5795 14.233 29.5446 14.2304C27.4768 14.2304 25.7946 15.9125 25.7946 17.9804C25.7946 20.0482 27.4768 21.7304 29.5446 21.7304C29.5795 21.7277 29.6143 21.725 29.6464 21.725L30.75 21.6929V25.0411C30.75 26.7982 29.3197 28.2286 27.5625 28.2286ZM2.89286 23.75V25.0438C2.89286 25.6196 3.36161 26.0884 3.93751 26.0884H27.5625C28.1384 26.0884 28.6071 25.6196 28.6071 25.0438V23.8036C25.8027 23.3536 23.6518 20.9161 23.6518 17.9857C23.6518 15.0554 25.8027 12.6179 28.6071 12.1679V11.1955C28.6071 10.6196 28.1384 10.1509 27.5625 10.1509H3.93751C3.36161 10.1509 2.89286 10.6196 2.89286 11.1955V12.2214C5.59286 12.7732 7.58036 15.133 7.58036 17.9884C7.58036 20.8438 5.59286 23.2036 2.89286 23.7554V23.75Z"
        fill="currentColor"
      />
      <path
        d="M13.9286 17.4313C13.0125 17.4313 12.2786 17.1527 11.7241 16.5982C11.1723 16.0438 10.8964 15.3098 10.8964 14.3991C10.8964 13.4884 11.1723 12.7357 11.7241 12.1813C12.2759 11.6268 13.0098 11.3482 13.9286 11.3482C14.8473 11.3482 15.5679 11.6241 16.117 12.1759C16.6661 12.7277 16.942 13.4697 16.942 14.3991C16.942 15.3286 16.6661 16.0411 16.117 16.5982C15.5679 17.1527 14.8366 17.4313 13.9286 17.4313ZM12.2893 22.025L11.1884 20.5089L20.1804 14.4902L21.2813 15.95L12.2893 22.025ZM13.9286 15.425C14.2045 15.425 14.4268 15.3313 14.5982 15.1438C14.7697 14.9563 14.8527 14.7072 14.8527 14.3991C14.8527 14.0911 14.767 13.8339 14.5982 13.6411C14.4295 13.4509 14.2072 13.3545 13.9286 13.3545C13.65 13.3545 13.4116 13.4509 13.2402 13.6411C13.0714 13.8339 12.9857 14.0857 12.9857 14.3991C12.9857 14.7125 13.0714 14.9536 13.2402 15.1438C13.4089 15.3339 13.6393 15.425 13.9286 15.425ZM18.6938 25.0973C17.7777 25.0973 17.0411 24.8188 16.4893 24.2643C15.9375 23.7098 15.6616 22.9759 15.6616 22.0652C15.6616 21.1545 15.9375 20.4018 16.4893 19.8473C17.0411 19.2929 17.775 19.0143 18.6938 19.0143C19.6125 19.0143 20.3331 19.2929 20.8822 19.8473C21.4313 20.4018 21.7072 21.1411 21.7072 22.0652C21.7072 22.9893 21.4313 23.7071 20.8822 24.2643C20.3331 24.8214 19.6018 25.0973 18.6938 25.0973ZM18.6938 23.0911C18.9697 23.0911 19.192 22.9973 19.3634 22.8098C19.5348 22.6223 19.6179 22.3732 19.6179 22.0652C19.6179 21.7572 19.5322 21.4973 19.3634 21.3072C19.1947 21.117 18.9723 21.0205 18.6938 21.0205C18.4152 21.0205 18.1768 21.117 18.0054 21.3072C17.834 21.4973 17.7509 21.7518 17.7509 22.0652C17.7509 22.3786 17.8366 22.6197 18.0054 22.8098C18.1741 23 18.4045 23.0911 18.6938 23.0911Z"
        fill="currentColor"
      />
    </Icon>
  )
}

function CouponFilledIcon() {
  return (
    <Icon viewBox="0 0 32 32" color="primary">
      <path
        d="M18.6937 21.0152C18.4044 21.0152 18.1768 21.1116 18.0054 21.3018C17.8339 21.492 17.7509 21.7464 17.7509 22.0598C17.7509 22.3732 17.8366 22.6143 18.0054 22.8045C18.1741 22.9946 18.4044 23.0857 18.6937 23.0857C18.983 23.0857 19.1919 22.992 19.3634 22.8045C19.5348 22.617 19.6178 22.3679 19.6178 22.0598C19.6178 21.7518 19.5321 21.492 19.3634 21.3018C19.1946 21.1116 18.9723 21.0152 18.6937 21.0152Z"
        fill="currentColor"
      />
      <path
        d="M13.9285 15.4223C14.2044 15.4223 14.4268 15.3286 14.5982 15.1411C14.7696 14.9536 14.8527 14.7045 14.8527 14.3964C14.8527 14.0884 14.7669 13.8312 14.5982 13.6384C14.4294 13.4482 14.2071 13.3518 13.9285 13.3518C13.65 13.3518 13.4116 13.4482 13.2401 13.6384C13.0714 13.8312 12.9857 14.083 12.9857 14.3964C12.9857 14.7098 13.0714 14.9509 13.2401 15.1411C13.4089 15.3313 13.6393 15.4223 13.9285 15.4223Z"
        fill="currentColor"
      />
      <path
        d="M29.5446 14.2304C29.5795 14.233 29.6116 14.2357 29.6464 14.2357L30.75 14.2679V11.1875C30.75 9.43036 29.3197 8 27.5625 8H3.93751C2.18036 8 0.75 9.43036 0.75 11.1875V14.2063L1.79197 14.2357C3.83572 14.292 5.4375 15.9366 5.4375 17.9804C5.4375 20.0241 3.83572 21.6687 1.79197 21.725L0.75 21.7545V25.0411C0.75 26.7982 2.18036 28.2286 3.93751 28.2286H27.5625C29.3197 28.2286 30.75 26.7982 30.75 25.0411V21.6929L29.6464 21.725C29.6116 21.725 29.5795 21.7277 29.5446 21.7304C27.4768 21.7304 25.7946 20.0482 25.7946 17.9804C25.7946 15.9125 27.4768 14.2304 29.5446 14.2304ZM11.7241 12.1786C12.2759 11.6241 13.0098 11.3455 13.9286 11.3455C14.8473 11.3455 15.5679 11.6214 16.117 12.1732C16.6661 12.725 16.942 13.467 16.942 14.3964C16.942 15.3259 16.6661 16.0384 16.117 16.5955C15.5679 17.15 14.8366 17.4286 13.9286 17.4286C13.0205 17.4286 12.2786 17.15 11.7241 16.5955C11.1723 16.0411 10.8964 15.3071 10.8964 14.3964C10.8964 13.4857 11.1723 12.733 11.7241 12.1786ZM11.1884 20.5063L20.1804 14.4875L21.2813 15.9473L12.2893 22.0223L11.1884 20.5063ZM20.8822 24.2589C20.333 24.8134 19.6018 25.092 18.6937 25.092C17.7857 25.092 17.0411 24.8134 16.4893 24.2589C15.9375 23.7045 15.6616 22.9705 15.6616 22.0598C15.6616 21.1491 15.9375 20.3964 16.4893 19.842C17.0411 19.2875 17.775 19.0089 18.6937 19.0089C19.6125 19.0089 20.333 19.2875 20.8822 19.842C21.4313 20.3964 21.7071 21.1357 21.7071 22.0598C21.7071 22.9839 21.4313 23.7018 20.8822 24.2589Z"
        fill="currentColor"
      />
    </Icon>
  )
}
