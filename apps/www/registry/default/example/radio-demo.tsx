import { useState } from "react"
import { Radio, RadioGroup, Typography } from "@design-systems/apollo-ui"

export default function RadioDemo() {
  const [selectedOptions, setSelectedOptions] = useState<number>(1)

  return (
    <div className="flex flex-col justify-start items-start">
      <Typography level="h4">Vertical</Typography>
      <RadioGroup value={selectedOptions} onChange={setSelectedOptions}>
        <Radio label="Option1" value={1} />
        <Radio label="Option2" value={2} />
        <Radio label="Disabled Option" disabled value={3} />
      </RadioGroup>
      <br />
      <Typography level="h4">Horizontal</Typography>
      <RadioGroup
        direction="horizontal"
        value={selectedOptions}
        onChange={setSelectedOptions}
      >
        <Radio label="Option1" value={1} />
        <Radio label="Option2" value={2} />
        <Radio label="Disabled Option" disabled value={3} />
      </RadioGroup>
      <br />
      <Typography level="h4">Disabled Group</Typography>
      <RadioGroup
        disabled
        value={selectedOptions}
        onChange={setSelectedOptions}
      >
        <Radio label="Option1" value={1} />
        <Radio label="Option2" value={2} />
        <Radio label="Disabled Option" value={3} />
      </RadioGroup>
    </div>
  )
}
