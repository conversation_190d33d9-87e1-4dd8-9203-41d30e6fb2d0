import React, { useState } from "react"
import { QuestionCircle } from "@design-systems/apollo-icons"
import { Button, Modal } from "@design-systems/apollo-ui"

export default function ModalWithIcon() {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <Button variant="solid" onClick={() => setIsOpen(true)}>
        Open Modal with Icon
      </Button>
      <Modal
        onOk={() => {
          console.log("[Click Event]: I'm okay")
        }}
        onCancel={() => {
          console.log("[Click Event]: Canceled")
        }}
        open={isOpen}
        onClose={() => {
          setIsOpen(false)
        }}
        header="Modal with Icon"
        icon={<QuestionCircle className="w-[16px] h-[16px]" />}
      >
        <div>
          This modal includes an icon. Once upon a time, there was a forest
          where plenty of birds lived and built their nests on the trees.
        </div>
      </Modal>
    </>
  )
}
