import { ChangeEventHandler, useCallback, useState } from "react"
import { Radio } from "@design-systems/apollo-ui"

export default function RadioStandaloneDemo() {
  const [selectedValue, setSelectedValue] = useState("Foo")

  const handleChange: ChangeEventHandler<HTMLInputElement> = useCallback(
    (event) => {
      setSelectedValue(event.target.value)
    },
    []
  )

  return (
    <div className="flex flex-col gap-2 justify-start items-start">
      <Radio
        label="Foo"
        value="Foo"
        checked={selectedValue === "Foo"}
        onChange={handleChange}
      />
      <Radio
        label="Bar"
        value="Bar"
        checked={selectedValue === "Bar"}
        onChange={handleChange}
      />
      <Radio
        label="Disabled"
        value="None"
        checked={selectedValue === "None"}
        disabled
        onChange={handleChange}
      />
    </div>
  )
}
