import { useState } from "react"
import { Typography, UploadBox } from "@/components"

export default function UploadBoxErrorStateDemo() {
  const [file, setFile] = useState<File | null>()
  const [files, setFiles] = useState<File[] | null>()

  const handleDeleteSingleFile = () => {
    setFile(null)
  }

  const handleDeleteMultipleFile = (deleteFileIndex: number) => {
    setFiles((prev) => prev?.filter((_, index) => index !== deleteFileIndex))
  }

  return (
    <div className="p-4 w-full flex flex-col gap-2 justify-start items-start">
      <Typography level="h3">Single File</Typography>
      <UploadBox
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        value={file}
        onDelete={handleDeleteSingleFile}
        maxFileSizeInBytes={24 * 1024 * 1024}
        onUpload={setFile}
        errorMessage="Lorem Ipsum Error"
      />
      <Typography level="h3">Multiple Files</Typography>
      <UploadBox
        fullWidth
        label="File Upload: "
        allowedFilesExtension={["jpg", "png"]}
        required
        multiple
        onDelete={handleDeleteMultipleFile}
        maxFileSizeInBytes={24 * 1024 * 1024}
        onUpload={setFiles}
        value={files}
        errorMessage="Lorem Ipsum Error"
      />
    </div>
  )
}
