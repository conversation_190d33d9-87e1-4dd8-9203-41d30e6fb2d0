import {
  Tab,
  <PERSON>b<PERSON>anel,
  <PERSON><PERSON>,
  <PERSON>bsList,
  Typography,
} from "@design-systems/apollo-ui"

export default function TabsVariantsDemo() {
  return (
    <div className="w-full flex flex-col gap-4 justify-start items-start p-4">
      <div className="w-full flex flex-col justify-start items-start">
        <Typography level="h4">Fill (Default)</Typography>
        <Tabs fullWidth defaultValue={0}>
          <TabsList variant="scrollable" scrollButtons>
            <Tab value={0}>Tab 1</Tab>
            <Tab value={1}>Tab 2</Tab>
            <Tab value={2}>Tab 3</Tab>
            <Tab value={3}>Tab 4</Tab>
          </TabsList>

          <TabPanel value={0}>Page for Tab 1</TabPanel>
          <TabPanel value={1}>Page for Tab 2</TabPanel>
          <TabPanel value={2}>Page for Tab 3</TabPanel>
          <TabPanel value={3}>Page for Tab 4</TabPanel>
        </Tabs>
      </div>
      <div className="w-full flex flex-col justify-start items-start">
        <Typography level="h4">Fit</Typography>
        <Tabs fullWidth defaultValue={0}>
          <TabsList variant="scrollable" scrollButtons>
            <Tab variant="fit" value={0}>
              Tab 1
            </Tab>
            <Tab variant="fit" value={1}>
              Tab 2
            </Tab>
            <Tab variant="fit" value={2}>
              Tab 3
            </Tab>
            <Tab variant="fit" value={3}>
              Tab 4
            </Tab>
          </TabsList>

          <TabPanel value={0}>Page for Tab 1</TabPanel>
          <TabPanel value={1}>Page for Tab 2</TabPanel>
          <TabPanel value={2}>Page for Tab 3</TabPanel>
          <TabPanel value={3}>Page for Tab 4</TabPanel>
        </Tabs>
      </div>
    </div>
  )
}
