"use client"

import type { PropsWithChildren } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Chip,
  SidebarLayout,
  SidebarMenu,
  useSidebar,
  type MenuItemConfig,
} from "@/components"

import { docsConfig } from "@/config/docs"
import { siteConfig } from "@/config/site"

import { AIBubble } from "./ai-bubble"
import { Icons } from "./icons"
import { SiteHeader } from "./site-header"

const sidebarMenus: SidebarMenu[] = parseDocsConfigToSidebarMenus()

export function AppSidebarLayout({ children }: PropsWithChildren) {
  const pathname = usePathname()

  const { allGroupKey, sidebarProps } = useSidebar(sidebarMenus)

  return (
    <SidebarLayout
      width="250px"
      expandedMenuKeys={allGroupKey}
      selectedMenuKey={pathname ?? ""}
      header={
        <div className="flex flex-row gap-2 h-[56px] p-2">
          <Link href="/" className="flex items-center space-x-2">
            <span className="hidden font-bold sm:inline-block">
              {siteConfig.name}
            </span>
          </Link>
        </div>
      }
      {...sidebarProps}
    >
      <SiteHeader />
      {children}
    </SidebarLayout>
  )
}

/**
 * Recursively parse items from docsConfig into SidebarMenu format.
 */
function parseNavItems(items: any[]): MenuItemConfig[] {
  return items.map((item) => ({
    key: item.href ?? item.title,
    label: item.title,
    href: item.href,
    children: item.items?.length ? parseNavItems(item.items) : undefined,
    LinkComponent: Link,
    ...(item.labelVersion
      ? {
          endDecorator: (
            <Chip rounded="full" label={item.labelVersion} color="primary" />
          ),
        }
      : {}),
  }))
}

/**
 * Parse docsConfig into Sidebar `menus` prop format.
 */
function parseDocsConfigToSidebarMenus(): SidebarMenu[] {
  return docsConfig.sidebarNav.map((section) => ({
    key: section.title,
    label: section.title,
    items: parseNavItems(section.items),
    ...(section.labelVersion
      ? {
          endDecorator: (
            <Chip rounded="full" label={section.labelVersion} color="primary" />
          ),
        }
      : {}),
  }))
}
