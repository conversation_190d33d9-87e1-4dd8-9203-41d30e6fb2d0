{"/Users/<USER>/Documents/workspace/scan-pool/cj-npd-web/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 65, "props": [{"prop_name": "level", "prop_usage_count": 62}, {"prop_name": "className", "prop_usage_count": 45}, {"prop_name": "color", "prop_usage_count": 2}, {"prop_name": "key", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 33, "props": [{"prop_name": "onClick", "prop_usage_count": 28}, {"prop_name": "type", "prop_usage_count": 19}, {"prop_name": "variant", "prop_usage_count": 14}, {"prop_name": "disabled", "prop_usage_count": 12}, {"prop_name": "loading", "prop_usage_count": 11}, {"prop_name": "size", "prop_usage_count": 11}, {"prop_name": "startDecorator", "prop_usage_count": 5}, {"prop_name": "className", "prop_usage_count": 5}, {"prop_name": "loadingPosition", "prop_usage_count": 4}, {"prop_name": "color", "prop_usage_count": 2}, {"prop_name": "endDecorator", "prop_usage_count": 1}, {"prop_name": "id", "prop_usage_count": 1}, {"prop_name": "tabIndex", "prop_usage_count": 1}]}, {"component": "Input", "usage_count": 11, "props": [{"prop_name": "type", "prop_usage_count": 11}, {"prop_name": "error", "prop_usage_count": 10}, {"prop_name": "placeholder", "prop_usage_count": 10}, {"prop_name": "className", "prop_usage_count": 7}, {"prop_name": "disabled", "prop_usage_count": 3}, {"prop_name": "onChange", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}, {"prop_name": "onBlur", "prop_usage_count": 1}, {"prop_name": "endDecorator", "prop_usage_count": 1}, {"prop_name": "autoFocus", "prop_usage_count": 1}, {"prop_name": "name", "prop_usage_count": 1}]}, {"component": "Option", "usage_count": 5, "props": [{"prop_name": "value", "prop_usage_count": 5}, {"prop_name": "key", "prop_usage_count": 4}]}, {"component": "Breadcrumbs", "usage_count": 1, "props": [{"prop_name": "aria-label", "prop_usage_count": 1}]}, {"component": "Select", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "name", "prop_usage_count": 1}, {"prop_name": "onChange", "prop_usage_count": 1}, {"prop_name": "defaultValue", "prop_usage_count": 1}]}], "Icon component": [{"component": "Close", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "onClick", "prop_usage_count": 3}]}, {"component": "ExclamationCircle", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "width", "prop_usage_count": 1}]}, {"component": "DeleteOutlined", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}]}, {"component": "CloseCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "Eye", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "PlusCircle", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Redo", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Right", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Home", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "CheckCircle", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "QuestionCircle", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "InfoCircle", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Upload", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "PaperClip", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "EyeInvisible", "usage_count": 1, "props": []}]}, "/Users/<USER>/Documents/workspace/scan-pool/cj-tote-store-web/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 145, "props": [{"prop_name": "level", "prop_usage_count": 99}, {"prop_name": "className", "prop_usage_count": 68}, {"prop_name": "color", "prop_usage_count": 8}, {"prop_name": "key", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 100, "props": [{"prop_name": "onClick", "prop_usage_count": 98}, {"prop_name": "variant", "prop_usage_count": 63}, {"prop_name": "disabled", "prop_usage_count": 61}, {"prop_name": "size", "prop_usage_count": 47}, {"prop_name": "color", "prop_usage_count": 40}, {"prop_name": "id", "prop_usage_count": 40}, {"prop_name": "loading", "prop_usage_count": 38}, {"prop_name": "className", "prop_usage_count": 38}, {"prop_name": "loadingPosition", "prop_usage_count": 21}, {"prop_name": "fullWidth", "prop_usage_count": 13}, {"prop_name": "name", "prop_usage_count": 12}, {"prop_name": "startDecorator", "prop_usage_count": 4}, {"prop_name": "loadingIndicator", "prop_usage_count": 2}]}, {"component": "Option", "usage_count": 49, "props": [{"prop_name": "value", "prop_usage_count": 49}, {"prop_name": "ref", "prop_usage_count": 6}, {"prop_name": "id", "prop_usage_count": 4}, {"prop_name": "key", "prop_usage_count": 4}]}, {"component": "Input", "usage_count": 22, "props": [{"prop_name": "placeholder", "prop_usage_count": 21}, {"prop_name": "onChange", "prop_usage_count": 21}, {"prop_name": "value", "prop_usage_count": 20}, {"prop_name": "disabled", "prop_usage_count": 19}, {"prop_name": "id", "prop_usage_count": 17}, {"prop_name": "className", "prop_usage_count": 13}, {"prop_name": "onInput", "prop_usage_count": 12}, {"prop_name": "slotProps", "prop_usage_count": 9}, {"prop_name": "error", "prop_usage_count": 8}, {"prop_name": "name", "prop_usage_count": 5}, {"prop_name": "onKeyUp", "prop_usage_count": 5}, {"prop_name": "onKeyDown", "prop_usage_count": 4}, {"prop_name": "color", "prop_usage_count": 4}, {"prop_name": "endDecorator", "prop_usage_count": 3}, {"prop_name": "multiline", "prop_usage_count": 3}, {"prop_name": "rows", "prop_usage_count": 3}, {"prop_name": "autoComplete", "prop_usage_count": 3}, {"prop_name": "readOnly", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "DatePicker", "usage_count": 15, "props": [{"prop_name": "value", "prop_usage_count": 15}, {"prop_name": "onChange", "prop_usage_count": 15}, {"prop_name": "id", "prop_usage_count": 14}, {"prop_name": "name", "prop_usage_count": 14}, {"prop_name": "minDate", "prop_usage_count": 14}, {"prop_name": "disabled", "prop_usage_count": 13}, {"prop_name": "maxDate", "prop_usage_count": 13}, {"prop_name": "placeholder", "prop_usage_count": 6}, {"prop_name": "error", "prop_usage_count": 5}, {"prop_name": "format", "prop_usage_count": 2}, {"prop_name": "onlyMonthPicker", "prop_usage_count": 1}, {"prop_name": "hideYear", "prop_usage_count": 1}, {"prop_name": "buttons", "prop_usage_count": 1}, {"prop_name": "only<PERSON>ear<PERSON>icker", "prop_usage_count": 1}]}, {"component": "Select", "usage_count": 13, "props": [{"prop_name": "className", "prop_usage_count": 13}, {"prop_name": "onChange", "prop_usage_count": 13}, {"prop_name": "id", "prop_usage_count": 12}, {"prop_name": "name", "prop_usage_count": 12}, {"prop_name": "value", "prop_usage_count": 12}, {"prop_name": "disabled", "prop_usage_count": 12}, {"prop_name": "placeholder", "prop_usage_count": 5}, {"prop_name": "multiple", "prop_usage_count": 3}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Chip", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "color", "prop_usage_count": 4}, {"prop_name": "label", "prop_usage_count": 4}, {"prop_name": "key", "prop_usage_count": 4}, {"prop_name": "onDelete", "prop_usage_count": 4}]}, {"component": "Tab", "usage_count": 2, "props": [{"prop_name": "value", "prop_usage_count": 2}]}, {"component": "TabPanel", "usage_count": 2, "props": [{"prop_name": "value", "prop_usage_count": 2}]}, {"component": "Checkbox", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "label", "prop_usage_count": 2}, {"prop_name": "checked", "prop_usage_count": 2}, {"prop_name": "onChange", "prop_usage_count": 2}, {"prop_name": "indeterminate", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}, {"prop_name": "value", "prop_usage_count": 1}]}, {"component": "Tabs", "usage_count": 1, "props": [{"prop_name": "defaultValue", "prop_usage_count": 1}]}, {"component": "TabsList", "usage_count": 1, "props": [{"prop_name": "variant", "prop_usage_count": 1}, {"prop_name": "scrollButtons", "prop_usage_count": 1}]}, {"component": "Breadcrumbs", "usage_count": 1, "props": [{"prop_name": "aria-label", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}], "Icon component": [{"component": "ExclamationCircle", "usage_count": 11, "props": [{"prop_name": "className", "prop_usage_count": 10}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "DeleteOutlined", "usage_count": 10, "props": [{"prop_name": "className", "prop_usage_count": 9}, {"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Close", "usage_count": 6, "props": [{"prop_name": "className", "prop_usage_count": 6}, {"prop_name": "onClick", "prop_usage_count": 3}]}, {"component": "Edit", "usage_count": 5, "props": [{"prop_name": "className", "prop_usage_count": 5}]}, {"component": "CloseCircle", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Eye", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}]}, {"component": "CheckCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "QuestionCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "InfoCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Loading3Quarters", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "Down", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Search", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 1, "props": [{"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Up", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Logout", "usage_count": 1, "props": []}, {"component": "Bulb", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}]}, "/Users/<USER>/Documents/workspace/scan-pool/cj-trade-agreement-web/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 66, "props": [{"prop_name": "level", "prop_usage_count": 44}, {"prop_name": "className", "prop_usage_count": 38}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "key", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 59, "props": [{"prop_name": "variant", "prop_usage_count": 56}, {"prop_name": "onClick", "prop_usage_count": 55}, {"prop_name": "loading", "prop_usage_count": 39}, {"prop_name": "disabled", "prop_usage_count": 37}, {"prop_name": "loadingPosition", "prop_usage_count": 34}, {"prop_name": "className", "prop_usage_count": 33}, {"prop_name": "id", "prop_usage_count": 22}, {"prop_name": "color", "prop_usage_count": 13}, {"prop_name": "name", "prop_usage_count": 8}, {"prop_name": "size", "prop_usage_count": 6}, {"prop_name": "startDecorator", "prop_usage_count": 2}, {"prop_name": "fullWidth", "prop_usage_count": 1}]}, {"component": "Input", "usage_count": 22, "props": [{"prop_name": "placeholder", "prop_usage_count": 22}, {"prop_name": "value", "prop_usage_count": 21}, {"prop_name": "disabled", "prop_usage_count": 21}, {"prop_name": "slotProps", "prop_usage_count": 21}, {"prop_name": "id", "prop_usage_count": 20}, {"prop_name": "onChange", "prop_usage_count": 18}, {"prop_name": "onInput", "prop_usage_count": 13}, {"prop_name": "className", "prop_usage_count": 9}, {"prop_name": "error", "prop_usage_count": 6}, {"prop_name": "onBlur", "prop_usage_count": 1}, {"prop_name": "onKeyDown", "prop_usage_count": 1}]}, {"component": "Toast", "usage_count": 16, "props": [{"prop_name": "position", "prop_usage_count": 16}, {"prop_name": "open", "prop_usage_count": 16}, {"prop_name": "title", "prop_usage_count": 16}, {"prop_name": "description", "prop_usage_count": 16}, {"prop_name": "severity", "prop_usage_count": 16}, {"prop_name": "onClose", "prop_usage_count": 16}, {"prop_name": "autoHideDuration", "prop_usage_count": 2}, {"prop_name": "exited", "prop_usage_count": 2}, {"prop_name": "lineClamp", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "DateInput", "usage_count": 9, "props": [{"prop_name": "id", "prop_usage_count": 9}, {"prop_name": "placeholder", "prop_usage_count": 9}, {"prop_name": "era", "prop_usage_count": 9}, {"prop_name": "locale", "prop_usage_count": 9}, {"prop_name": "value", "prop_usage_count": 9}, {"prop_name": "disabled", "prop_usage_count": 9}, {"prop_name": "onChange", "prop_usage_count": 9}, {"prop_name": "name", "prop_usage_count": 8}, {"prop_name": "maxDate", "prop_usage_count": 6}, {"prop_name": "minDate", "prop_usage_count": 3}, {"prop_name": "format", "prop_usage_count": 2}, {"prop_name": "showYearPicker", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "showMonthYearPicker", "prop_usage_count": 1}]}, {"component": "Tab", "usage_count": 7, "props": [{"prop_name": "value", "prop_usage_count": 7}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "TabPanel", "usage_count": 7, "props": [{"prop_name": "value", "prop_usage_count": 7}]}, {"component": "Option", "usage_count": 5, "props": [{"prop_name": "value", "prop_usage_count": 5}, {"prop_name": "key", "prop_usage_count": 4}, {"prop_name": "id", "prop_usage_count": 1}]}, {"component": "Select", "usage_count": 4, "props": [{"prop_name": "id", "prop_usage_count": 4}, {"prop_name": "name", "prop_usage_count": 4}, {"prop_name": "placeholder", "prop_usage_count": 4}, {"prop_name": "value", "prop_usage_count": 4}, {"prop_name": "disabled", "prop_usage_count": 4}, {"prop_name": "onChange", "prop_usage_count": 4}, {"prop_name": "multiple", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Tabs", "usage_count": 2, "props": [{"prop_name": "defaultValue", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "TabsList", "usage_count": 2, "props": [{"prop_name": "variant", "prop_usage_count": 2}, {"prop_name": "scrollButtons", "prop_usage_count": 2}]}, {"component": "ThemeProvider", "usage_count": 1, "props": []}, {"component": "Modal", "usage_count": 1, "props": [{"prop_name": "open", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}]}, {"component": "Autocomplete", "usage_count": 1, "props": [{"prop_name": "open", "prop_usage_count": 1}, {"prop_name": "options", "prop_usage_count": 1}, {"prop_name": "error", "prop_usage_count": 1}, {"prop_name": "key", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}, {"prop_name": "placeholder", "prop_usage_count": 1}, {"prop_name": "value", "prop_usage_count": 1}, {"prop_name": "inputValue", "prop_usage_count": 1}, {"prop_name": "onInputChange", "prop_usage_count": 1}, {"prop_name": "onChange", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}, {"prop_name": "filterOptions", "prop_usage_count": 1}, {"prop_name": "inputRef", "prop_usage_count": 1}]}, {"component": "IconButton", "usage_count": 1, "props": [{"prop_name": "onClick", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}]}, {"component": "Breadcrumbs", "usage_count": 1, "props": [{"prop_name": "aria-label", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}], "Icon component": [{"component": "DeleteOutlined", "usage_count": 7, "props": [{"prop_name": "className", "prop_usage_count": 7}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Download", "usage_count": 6, "props": [{"prop_name": "className", "prop_usage_count": 6}]}, {"component": "ExclamationCircle", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Close", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "onClick", "prop_usage_count": 3}]}, {"component": "CloseCircle", "usage_count": 3, "props": [{"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "QuestionCircle", "usage_count": 3, "props": [{"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Plus", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "InfoCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "width", "prop_usage_count": 1}, {"prop_name": "height", "prop_usage_count": 1}]}, {"component": "Edit", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "History", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "PaperClip", "usage_count": 1, "props": [{"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Upload", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 1, "props": [{"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Up", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Down", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Logout", "usage_count": 1, "props": []}]}, "/Users/<USER>/Documents/workspace/scan-pool/pms-web-portal/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 5, "props": [{"prop_name": "level", "prop_usage_count": 4}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "ThemeProvider", "usage_count": 2, "props": [{"prop_name": "theme", "prop_usage_count": 2}]}, {"component": "ToastProvider", "usage_count": 2, "props": [{"prop_name": "autoHideDuration", "prop_usage_count": 1}]}, {"component": "Breadcrumbs", "usage_count": 2, "props": []}, {"component": "Autocomplete", "usage_count": 2, "props": [{"prop_name": "label", "prop_usage_count": 2}, {"prop_name": "placeholder", "prop_usage_count": 2}, {"prop_name": "options", "prop_usage_count": 2}, {"prop_name": "value", "prop_usage_count": 2}, {"prop_name": "error", "prop_usage_count": 2}, {"prop_name": "helperText", "prop_usage_count": 2}, {"prop_name": "multiple", "prop_usage_count": 2}, {"prop_name": "hasSelectAll", "prop_usage_count": 2}, {"prop_name": "fullWidth", "prop_usage_count": 2}, {"prop_name": "onChange", "prop_usage_count": 2}, {"prop_name": "disabled", "prop_usage_count": 2}]}, {"component": "<PERSON><PERSON>", "usage_count": 2, "props": [{"prop_name": "loading", "prop_usage_count": 2}, {"prop_name": "type", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}, {"prop_name": "startDecorator", "prop_usage_count": 1}, {"prop_name": "variant", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "TabsList", "usage_count": 1, "props": [{"prop_name": "variant", "prop_usage_count": 1}, {"prop_name": "scrollButtons", "prop_usage_count": 1}]}, {"component": "Tab", "usage_count": 1, "props": [{"prop_name": "value", "prop_usage_count": 1}, {"prop_name": "key", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Option", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "key", "prop_usage_count": 1}, {"prop_name": "value", "prop_usage_count": 1}]}, {"component": "DatePicker", "usage_count": 1, "props": [{"prop_name": "id", "prop_usage_count": 1}, {"prop_name": "disableDayPicker", "prop_usage_count": 1}, {"prop_name": "timeFormat", "prop_usage_count": 1}, {"prop_name": "format", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Checkbox", "usage_count": 1, "props": [{"prop_name": "checked", "prop_usage_count": 1}, {"prop_name": "onChange", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 1, "props": [{"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "description", "prop_usage_count": 1}, {"prop_name": "fullWidth", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}], "Icon component": [{"component": "QuestionCircle", "usage_count": 10, "props": [{"prop_name": "size", "prop_usage_count": 10}]}, {"component": "DeleteOutlined", "usage_count": 7, "props": [{"prop_name": "size", "prop_usage_count": 7}]}, {"component": "Close", "usage_count": 6, "props": [{"prop_name": "size", "prop_usage_count": 6}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Plus", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 4}]}, {"component": "Left", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "onClick", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Down", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "CloseCircle", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Sync", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "onClick", "prop_usage_count": 2}]}, {"component": "CheckCircle", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Appstore", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Notification", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "User", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Logout", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Right", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "History", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Eye", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "EyeInvisible", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Upload", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "size", "prop_usage_count": 2}]}, {"component": "InfoCircle", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "More", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "ClockCircle", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "ExclamationCircle", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "AreaChart", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Edit", "usage_count": 1, "props": [{"prop_name": "id", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Up", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Comment", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "CaretUp", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "CaretDown", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Save", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "File", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Download", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Check", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Shop", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "DoubleRight", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "DoubleLeft", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Link", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "PlusCircle", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "MinusCircle", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Copy", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Search", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}]}, "/Users/<USER>/Documents/workspace/scan-pool/lineoa-app/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 31, "props": [{"prop_name": "className", "prop_usage_count": 31}, {"prop_name": "level", "prop_usage_count": 21}, {"prop_name": "align", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 14, "props": [{"prop_name": "onClick", "prop_usage_count": 13}, {"prop_name": "fullWidth", "prop_usage_count": 12}, {"prop_name": "variant", "prop_usage_count": 8}, {"prop_name": "className", "prop_usage_count": 7}, {"prop_name": "color", "prop_usage_count": 5}, {"prop_name": "disabled", "prop_usage_count": 2}, {"prop_name": "loading", "prop_usage_count": 2}]}, {"component": "Tab", "usage_count": 3, "props": [{"prop_name": "className", "prop_usage_count": 3}, {"prop_name": "variant", "prop_usage_count": 3}, {"prop_name": "value", "prop_usage_count": 3}]}, {"component": "TabPanel", "usage_count": 3, "props": [{"prop_name": "value", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Icon", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "ThemeProvider", "usage_count": 1, "props": [{"prop_name": "theme", "prop_usage_count": 1}]}, {"component": "CapsuleTab", "usage_count": 1, "props": [{"prop_name": "selectedIndex", "prop_usage_count": 1}, {"prop_name": "onSelect", "prop_usage_count": 1}, {"prop_name": "tabs", "prop_usage_count": 1}]}, {"component": "Tabs", "usage_count": 1, "props": [{"prop_name": "fullWidth", "prop_usage_count": 1}, {"prop_name": "defaultValue", "prop_usage_count": 1}]}, {"component": "TabsList", "usage_count": 1, "props": []}], "Icon component": [{"component": "ExclamationCircle", "usage_count": 2, "props": []}, {"component": "Right", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}]}, "/Users/<USER>/Documents/workspace/scan-pool/membership-admin-app/**/*.{js,jsx,ts,tsx}": {"UI component": [{"component": "Typography", "usage_count": 14, "props": [{"prop_name": "level", "prop_usage_count": 14}, {"prop_name": "className", "prop_usage_count": 13}]}, {"component": "<PERSON><PERSON>", "usage_count": 11, "props": [{"prop_name": "variant", "prop_usage_count": 10}, {"prop_name": "className", "prop_usage_count": 10}, {"prop_name": "onClick", "prop_usage_count": 6}, {"prop_name": "disabled", "prop_usage_count": 2}, {"prop_name": "type", "prop_usage_count": 2}, {"prop_name": "loading", "prop_usage_count": 2}, {"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Input", "usage_count": 5, "props": [{"prop_name": "error", "prop_usage_count": 5}, {"prop_name": "fullWidth", "prop_usage_count": 5}, {"prop_name": "helperText", "prop_usage_count": 5}, {"prop_name": "onChange", "prop_usage_count": 4}, {"prop_name": "placeholder", "prop_usage_count": 3}, {"prop_name": "type", "prop_usage_count": 2}, {"prop_name": "max<PERSON><PERSON><PERSON>", "prop_usage_count": 1}, {"prop_name": "onInput", "prop_usage_count": 1}]}, {"component": "Autocomplete", "usage_count": 3, "props": [{"prop_name": "placeholder", "prop_usage_count": 3}, {"prop_name": "options", "prop_usage_count": 3}, {"prop_name": "value", "prop_usage_count": 3}, {"prop_name": "multiple", "prop_usage_count": 3}, {"prop_name": "fullWidth", "prop_usage_count": 3}, {"prop_name": "onChange", "prop_usage_count": 3}, {"prop_name": "allOption", "prop_usage_count": 3}, {"prop_name": "hasSelectAll", "prop_usage_count": 3}, {"prop_name": "limitTags", "prop_usage_count": 1}]}, {"component": "Chip", "usage_count": 3, "props": [{"prop_name": "color", "prop_usage_count": 3}, {"prop_name": "label", "prop_usage_count": 3}, {"prop_name": "style", "prop_usage_count": 3}]}, {"component": "ThemeProvider", "usage_count": 1, "props": [{"prop_name": "theme", "prop_usage_count": 1}]}, {"component": "ToastProvider", "usage_count": 1, "props": []}, {"component": "Select", "usage_count": 1, "props": [{"prop_name": "onChange", "prop_usage_count": 1}, {"prop_name": "error", "prop_usage_count": 1}, {"prop_name": "placeholder", "prop_usage_count": 1}, {"prop_name": "fullWidth", "prop_usage_count": 1}, {"prop_name": "helperText", "prop_usage_count": 1}]}, {"component": "Option", "usage_count": 1, "props": [{"prop_name": "key", "prop_usage_count": 1}, {"prop_name": "value", "prop_usage_count": 1}]}, {"component": "Modal", "usage_count": 1, "props": [{"prop_name": "header", "prop_usage_count": 1}, {"prop_name": "open", "prop_usage_count": 1}, {"prop_name": "hideCloseIcon", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "title", "prop_usage_count": 1}, {"prop_name": "description", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}]}], "Icon component": [{"component": "InfoCircle", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Calendar", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "color", "prop_usage_count": 1}]}, {"component": "User", "usage_count": 1, "props": []}, {"component": "<PERSON><PERSON><PERSON>", "usage_count": 1, "props": []}, {"component": "Star", "usage_count": 1, "props": []}, {"component": "Edit", "usage_count": 1, "props": []}, {"component": "FileDone", "usage_count": 1, "props": []}]}, "summary": {"UI component": [{"component": "Typography", "usage_count": 326, "props": [{"prop_name": "level", "prop_usage_count": 244}, {"prop_name": "className", "prop_usage_count": 197}, {"prop_name": "color", "prop_usage_count": 11}, {"prop_name": "key", "prop_usage_count": 3}, {"prop_name": "style", "prop_usage_count": 1}, {"prop_name": "align", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 219, "props": [{"prop_name": "onClick", "prop_usage_count": 201}, {"prop_name": "variant", "prop_usage_count": 152}, {"prop_name": "disabled", "prop_usage_count": 115}, {"prop_name": "loading", "prop_usage_count": 94}, {"prop_name": "className", "prop_usage_count": 94}, {"prop_name": "size", "prop_usage_count": 66}, {"prop_name": "id", "prop_usage_count": 63}, {"prop_name": "color", "prop_usage_count": 61}, {"prop_name": "loadingPosition", "prop_usage_count": 59}, {"prop_name": "fullWidth", "prop_usage_count": 26}, {"prop_name": "type", "prop_usage_count": 22}, {"prop_name": "name", "prop_usage_count": 20}, {"prop_name": "startDecorator", "prop_usage_count": 12}, {"prop_name": "loadingIndicator", "prop_usage_count": 2}, {"prop_name": "endDecorator", "prop_usage_count": 1}, {"prop_name": "tabIndex", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Option", "usage_count": 61, "props": [{"prop_name": "value", "prop_usage_count": 61}, {"prop_name": "key", "prop_usage_count": 14}, {"prop_name": "ref", "prop_usage_count": 6}, {"prop_name": "id", "prop_usage_count": 5}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Input", "usage_count": 60, "props": [{"prop_name": "placeholder", "prop_usage_count": 56}, {"prop_name": "onChange", "prop_usage_count": 45}, {"prop_name": "disabled", "prop_usage_count": 43}, {"prop_name": "value", "prop_usage_count": 41}, {"prop_name": "id", "prop_usage_count": 37}, {"prop_name": "slotProps", "prop_usage_count": 30}, {"prop_name": "className", "prop_usage_count": 29}, {"prop_name": "error", "prop_usage_count": 29}, {"prop_name": "onInput", "prop_usage_count": 26}, {"prop_name": "type", "prop_usage_count": 13}, {"prop_name": "name", "prop_usage_count": 6}, {"prop_name": "onKeyDown", "prop_usage_count": 5}, {"prop_name": "onKeyUp", "prop_usage_count": 5}, {"prop_name": "fullWidth", "prop_usage_count": 5}, {"prop_name": "helperText", "prop_usage_count": 5}, {"prop_name": "endDecorator", "prop_usage_count": 4}, {"prop_name": "color", "prop_usage_count": 4}, {"prop_name": "multiline", "prop_usage_count": 3}, {"prop_name": "rows", "prop_usage_count": 3}, {"prop_name": "autoComplete", "prop_usage_count": 3}, {"prop_name": "onBlur", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}, {"prop_name": "autoFocus", "prop_usage_count": 1}, {"prop_name": "readOnly", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}, {"prop_name": "max<PERSON><PERSON><PERSON>", "prop_usage_count": 1}]}, {"component": "Select", "usage_count": 19, "props": [{"prop_name": "onChange", "prop_usage_count": 19}, {"prop_name": "name", "prop_usage_count": 17}, {"prop_name": "id", "prop_usage_count": 16}, {"prop_name": "value", "prop_usage_count": 16}, {"prop_name": "disabled", "prop_usage_count": 16}, {"prop_name": "className", "prop_usage_count": 15}, {"prop_name": "placeholder", "prop_usage_count": 10}, {"prop_name": "multiple", "prop_usage_count": 4}, {"prop_name": "color", "prop_usage_count": 2}, {"prop_name": "defaultValue", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}, {"prop_name": "error", "prop_usage_count": 1}, {"prop_name": "fullWidth", "prop_usage_count": 1}, {"prop_name": "helperText", "prop_usage_count": 1}]}, {"component": "DatePicker", "usage_count": 16, "props": [{"prop_name": "id", "prop_usage_count": 15}, {"prop_name": "value", "prop_usage_count": 15}, {"prop_name": "onChange", "prop_usage_count": 15}, {"prop_name": "name", "prop_usage_count": 14}, {"prop_name": "minDate", "prop_usage_count": 14}, {"prop_name": "disabled", "prop_usage_count": 13}, {"prop_name": "maxDate", "prop_usage_count": 13}, {"prop_name": "placeholder", "prop_usage_count": 6}, {"prop_name": "error", "prop_usage_count": 5}, {"prop_name": "format", "prop_usage_count": 3}, {"prop_name": "onlyMonthPicker", "prop_usage_count": 1}, {"prop_name": "hideYear", "prop_usage_count": 1}, {"prop_name": "buttons", "prop_usage_count": 1}, {"prop_name": "only<PERSON>ear<PERSON>icker", "prop_usage_count": 1}, {"prop_name": "disableDayPicker", "prop_usage_count": 1}, {"prop_name": "timeFormat", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Toast", "usage_count": 16, "props": [{"prop_name": "position", "prop_usage_count": 16}, {"prop_name": "open", "prop_usage_count": 16}, {"prop_name": "title", "prop_usage_count": 16}, {"prop_name": "description", "prop_usage_count": 16}, {"prop_name": "severity", "prop_usage_count": 16}, {"prop_name": "onClose", "prop_usage_count": 16}, {"prop_name": "autoHideDuration", "prop_usage_count": 2}, {"prop_name": "exited", "prop_usage_count": 2}, {"prop_name": "lineClamp", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Tab", "usage_count": 13, "props": [{"prop_name": "value", "prop_usage_count": 13}, {"prop_name": "className", "prop_usage_count": 6}, {"prop_name": "variant", "prop_usage_count": 3}, {"prop_name": "key", "prop_usage_count": 1}]}, {"component": "TabPanel", "usage_count": 12, "props": [{"prop_name": "value", "prop_usage_count": 12}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "DateInput", "usage_count": 9, "props": [{"prop_name": "id", "prop_usage_count": 9}, {"prop_name": "placeholder", "prop_usage_count": 9}, {"prop_name": "era", "prop_usage_count": 9}, {"prop_name": "locale", "prop_usage_count": 9}, {"prop_name": "value", "prop_usage_count": 9}, {"prop_name": "disabled", "prop_usage_count": 9}, {"prop_name": "onChange", "prop_usage_count": 9}, {"prop_name": "name", "prop_usage_count": 8}, {"prop_name": "maxDate", "prop_usage_count": 6}, {"prop_name": "minDate", "prop_usage_count": 3}, {"prop_name": "format", "prop_usage_count": 2}, {"prop_name": "showYearPicker", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "showMonthYearPicker", "prop_usage_count": 1}]}, {"component": "Chip", "usage_count": 7, "props": [{"prop_name": "color", "prop_usage_count": 7}, {"prop_name": "label", "prop_usage_count": 7}, {"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "key", "prop_usage_count": 4}, {"prop_name": "onDelete", "prop_usage_count": 4}, {"prop_name": "style", "prop_usage_count": 3}]}, {"component": "Autocomplete", "usage_count": 6, "props": [{"prop_name": "options", "prop_usage_count": 6}, {"prop_name": "placeholder", "prop_usage_count": 6}, {"prop_name": "value", "prop_usage_count": 6}, {"prop_name": "onChange", "prop_usage_count": 6}, {"prop_name": "multiple", "prop_usage_count": 5}, {"prop_name": "hasSelectAll", "prop_usage_count": 5}, {"prop_name": "fullWidth", "prop_usage_count": 5}, {"prop_name": "error", "prop_usage_count": 3}, {"prop_name": "disabled", "prop_usage_count": 3}, {"prop_name": "allOption", "prop_usage_count": 3}, {"prop_name": "label", "prop_usage_count": 2}, {"prop_name": "helperText", "prop_usage_count": 2}, {"prop_name": "open", "prop_usage_count": 1}, {"prop_name": "key", "prop_usage_count": 1}, {"prop_name": "inputValue", "prop_usage_count": 1}, {"prop_name": "onInputChange", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}, {"prop_name": "filterOptions", "prop_usage_count": 1}, {"prop_name": "inputRef", "prop_usage_count": 1}, {"prop_name": "limitTags", "prop_usage_count": 1}]}, {"component": "Breadcrumbs", "usage_count": 5, "props": [{"prop_name": "aria-label", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "TabsList", "usage_count": 5, "props": [{"prop_name": "variant", "prop_usage_count": 4}, {"prop_name": "scrollButtons", "prop_usage_count": 4}]}, {"component": "ThemeProvider", "usage_count": 5, "props": [{"prop_name": "theme", "prop_usage_count": 4}]}, {"component": "Tabs", "usage_count": 4, "props": [{"prop_name": "defaultValue", "prop_usage_count": 4}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "fullWidth", "prop_usage_count": 1}]}, {"component": "Checkbox", "usage_count": 3, "props": [{"prop_name": "checked", "prop_usage_count": 3}, {"prop_name": "onChange", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "label", "prop_usage_count": 2}, {"prop_name": "indeterminate", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}, {"prop_name": "value", "prop_usage_count": 1}]}, {"component": "ToastProvider", "usage_count": 3, "props": [{"prop_name": "autoHideDuration", "prop_usage_count": 1}]}, {"component": "Modal", "usage_count": 2, "props": [{"prop_name": "open", "prop_usage_count": 2}, {"prop_name": "onClose", "prop_usage_count": 2}, {"prop_name": "header", "prop_usage_count": 1}, {"prop_name": "hideCloseIcon", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON>", "usage_count": 2, "props": [{"prop_name": "color", "prop_usage_count": 2}, {"prop_name": "description", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "fullWidth", "prop_usage_count": 1}, {"prop_name": "title", "prop_usage_count": 1}, {"prop_name": "onClose", "prop_usage_count": 1}]}, {"component": "Icon", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "IconButton", "usage_count": 1, "props": [{"prop_name": "onClick", "prop_usage_count": 1}, {"prop_name": "disabled", "prop_usage_count": 1}]}, {"component": "CapsuleTab", "usage_count": 1, "props": [{"prop_name": "selectedIndex", "prop_usage_count": 1}, {"prop_name": "onSelect", "prop_usage_count": 1}, {"prop_name": "tabs", "prop_usage_count": 1}]}], "Icon component": [{"component": "DeleteOutlined", "usage_count": 28, "props": [{"prop_name": "className", "prop_usage_count": 20}, {"prop_name": "size", "prop_usage_count": 9}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 2}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "ExclamationCircle", "usage_count": 23, "props": [{"prop_name": "className", "prop_usage_count": 17}, {"prop_name": "width", "prop_usage_count": 5}, {"prop_name": "height", "prop_usage_count": 2}, {"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Close", "usage_count": 20, "props": [{"prop_name": "className", "prop_usage_count": 15}, {"prop_name": "onClick", "prop_usage_count": 9}, {"prop_name": "size", "prop_usage_count": 6}]}, {"component": "QuestionCircle", "usage_count": 16, "props": [{"prop_name": "size", "prop_usage_count": 10}, {"prop_name": "className", "prop_usage_count": 6}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 2}]}, {"component": "CloseCircle", "usage_count": 13, "props": [{"prop_name": "className", "prop_usage_count": 12}, {"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 2}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "InfoCircle", "usage_count": 8, "props": [{"prop_name": "className", "prop_usage_count": 8}, {"prop_name": "size", "prop_usage_count": 3}, {"prop_name": "width", "prop_usage_count": 2}, {"prop_name": "height", "prop_usage_count": 2}]}, {"component": "Eye", "usage_count": 8, "props": [{"prop_name": "className", "prop_usage_count": 5}, {"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Edit", "usage_count": 8, "props": [{"prop_name": "className", "prop_usage_count": 7}, {"prop_name": "id", "prop_usage_count": 1}, {"prop_name": "onClick", "prop_usage_count": 1}]}, {"component": "Down", "usage_count": 7, "props": [{"prop_name": "size", "prop_usage_count": 6}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Download", "usage_count": 7, "props": [{"prop_name": "className", "prop_usage_count": 7}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "CheckCircle", "usage_count": 6, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "size", "prop_usage_count": 3}]}, {"component": "Plus", "usage_count": 6, "props": [{"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "Right", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Upload", "usage_count": 4, "props": [{"prop_name": "className", "prop_usage_count": 4}, {"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Logout", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "Left", "usage_count": 4, "props": [{"prop_name": "size", "prop_usage_count": 4}, {"prop_name": "onClick", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "EyeInvisible", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Up", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 3}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "History", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Sync", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 3}, {"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "onClick", "prop_usage_count": 2}]}, {"component": "User", "usage_count": 3, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "PlusCircle", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "PaperClip", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "color", "prop_usage_count": 1}, {"prop_name": "style", "prop_usage_count": 1}]}, {"component": "Search", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Loading3Quarters", "usage_count": 2, "props": [{"prop_name": "className", "prop_usage_count": 2}]}, {"component": "<PERSON><PERSON>", "usage_count": 2, "props": [{"prop_name": "onClick", "prop_usage_count": 2}]}, {"component": "Appstore", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Notification", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "More", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}]}, {"component": "ClockCircle", "usage_count": 2, "props": [{"prop_name": "size", "prop_usage_count": 2}, {"prop_name": "className", "prop_usage_count": 2}]}, {"component": "Redo", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Home", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Bulb", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "AreaChart", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Comment", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "CaretUp", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "CaretDown", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}, {"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Save", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "File", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "className", "prop_usage_count": 1}]}, {"component": "Check", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Shop", "usage_count": 1, "props": [{"prop_name": "className", "prop_usage_count": 1}]}, {"component": "DoubleRight", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "DoubleLeft", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Link", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "MinusCircle", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Copy", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}]}, {"component": "Calendar", "usage_count": 1, "props": [{"prop_name": "size", "prop_usage_count": 1}, {"prop_name": "color", "prop_usage_count": 1}]}, {"component": "<PERSON><PERSON><PERSON>", "usage_count": 1, "props": []}, {"component": "Star", "usage_count": 1, "props": []}, {"component": "FileDone", "usage_count": 1, "props": []}]}}