{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 20, "numPassedTests": 131, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 20, "numTotalTests": 131, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1730458914790, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["<Accordion />"], "duration": 80, "failureDetails": [], "failureMessages": [], "fullName": "<Accordion /> should show 'My Title' when pass it as header prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show 'My Title' when pass it as header prop"}, {"ancestorTitles": ["<Accordion />"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "<Accordion /> should show 'Content' when pass it as children prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show 'Content' when pass it as children prop"}, {"ancestorTitles": ["<Accordion />"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "<Accordion /> should show Custom Component when pass it as header prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show Custom Component when pass it as header prop"}, {"ancestorTitles": ["<Accordion />"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "<Accordion /> should have not display content and the content should not available on the DOM when `disabled` is `true`", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should have not display content and the content should not available on the DOM when `disabled` is `true`"}], "endTime": 1730458919459, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Accordion/Accordion.test.tsx", "startTime": 1730458915660, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<Input />"], "duration": 43, "failureDetails": [], "failureMessages": [], "fullName": "<Input /> loads and displays input value", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "loads and displays input value"}, {"ancestorTitles": ["<Input />"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "<Input /> loads and displays input with primary color (default)", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "loads and displays input with primary color (default)"}, {"ancestorTitles": ["<Input />"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "<Input /> loads and displays input with error state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "loads and displays input with error state"}, {"ancestorTitles": ["<Input />"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "<Input /> loads and displays input with disabled state", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "loads and displays input with disabled state"}], "endTime": 1730458919490, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Input/Input.test.tsx", "startTime": 1730458915774, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UploadBox Component"], "duration": 110, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component renders the upload button", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the upload button"}, {"ancestorTitles": ["UploadBox Component"], "duration": 34, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component disables upload button when disabled prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "disables upload button when disabled prop is true"}, {"ancestorTitles": ["UploadBox Component"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component allows multiple files to be uploaded when multiple is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "allows multiple files to be uploaded when multiple is true"}, {"ancestorTitles": ["UploadBox Component"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component limits the number of files to the fileLimit", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "limits the number of files to the fileLimit"}, {"ancestorTitles": ["UploadBox Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component displays allowed file types in description", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "displays allowed file types in description"}, {"ancestorTitles": ["UploadBox Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component renders custom error message when provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders custom error message when provided"}, {"ancestorTitles": ["UploadBox Component"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "UploadBox Component shows the uploaded files count and file names when files are uploaded", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "shows the uploaded files count and file names when files are uploaded"}], "endTime": 1730458919586, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/UploadBox/UploadBox.test.tsx", "startTime": 1730458915650, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Select Component"], "duration": 97, "failureDetails": [], "failureMessages": [], "fullName": "Select Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["Select Component"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "Select Component displays helper text when provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "displays helper text when provided"}, {"ancestorTitles": ["Select Component"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "Select Component renders with an error color if error prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with an error color if error prop is true"}, {"ancestorTitles": ["Select Component"], "duration": 40, "failureDetails": [], "failureMessages": [], "fullName": "Select Component renders the correct icon based on open state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders the correct icon based on open state"}, {"ancestorTitles": ["Select Component"], "duration": 30, "failureDetails": [], "failureMessages": [], "fullName": "Select Component applies custom slot props for listbox", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies custom slot props for listbox"}, {"ancestorTitles": ["Select Component"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "Select Component supports disabled prop, preventing button interaction", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "supports disabled prop, preventing button interaction"}, {"ancestorTitles": ["Select Component"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Select Component supports required prop, rendering required indicator", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "supports required prop, rendering required indicator"}, {"ancestorTitles": ["Select Component"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "Select Component renders custom width when width prop is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders custom width when width prop is provided"}], "endTime": 1730458919632, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Select/Select.test.tsx", "startTime": 1730458915712, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<Modal />"], "duration": 166, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should render the default modal", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render the default modal"}, {"ancestorTitles": ["<Modal />"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should render the full-size modal", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render the full-size modal"}, {"ancestorTitles": ["<Modal />"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should close the modal when ESC key is pressed", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should close the modal when ESC key is pressed"}, {"ancestorTitles": ["<Modal />"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should close the modal when clicking on the backdrop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should close the modal when clicking on the backdrop"}, {"ancestorTitles": ["<Modal />"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should render modal with custom header and footer", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should render modal with custom header and footer"}, {"ancestorTitles": ["<Modal />"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should render modal with icon", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render modal with icon"}, {"ancestorTitles": ["<Modal />"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should trigger callback when OK button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should trigger callback when OK button is clicked"}, {"ancestorTitles": ["<Modal />"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should trigger callback when Cancel button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should trigger callback when <PERSON><PERSON> button is clicked"}, {"ancestorTitles": ["<Modal />"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should trigger callback when Delete button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should trigger callback when Delete button is clicked"}, {"ancestorTitles": ["<Modal />"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should hide OK button when Delete button is displayed", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should hide OK button when Delete button is displayed"}, {"ancestorTitles": ["<Modal />"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should disable buttons when respective props are passed", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should disable buttons when respective props are passed"}, {"ancestorTitles": ["<Modal />"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should render buttons with custom text", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should render buttons with custom text"}, {"ancestorTitles": ["<Modal />"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "<Modal /> should hide close icon when hideCloseIcon is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should hide close icon when hideCloseIcon is true"}], "endTime": 1730458919736, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Modal/Modal.test.tsx", "startTime": 1730458915798, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["FloatButton Component"], "duration": 70, "failureDetails": [], "failureMessages": [], "fullName": "FloatButton Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["FloatButton Component"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "FloatButton Component applies expanded state correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "applies expanded state correctly"}, {"ancestorTitles": ["FloatButton Component"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "FloatButton Component places the icon on the start by default", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "places the icon on the start by default"}, {"ancestorTitles": ["FloatButton Component"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "FloatButton Component places the icon on the end when iconSide is 'end'", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "places the icon on the end when iconSide is 'end'"}, {"ancestorTitles": ["FloatButton Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "FloatButton Component forwards ref correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "forwards ref correctly"}], "endTime": 1730458919743, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/FloatButton/FloatButton.test.tsx", "startTime": 1730458915857, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<Radio />"], "duration": 21, "failureDetails": [], "failureMessages": [], "fullName": "<Radio /> display label when pass it as a prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "display label when pass it as a prop"}, {"ancestorTitles": ["<Radio />"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "<Radio /> have `checked` attribute when pass `checked` into the prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "have `checked` attribute when pass `checked` into the prop"}, {"ancestorTitles": ["<Radio />"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "<Radio /> have `disabled` attribute when pass `disabled` into the prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "have `disabled` attribute when pass `disabled` into the prop"}, {"ancestorTitles": ["<Radio />"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "<Radio /> must called `onChange` function when click on radio button", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "must called `onChange` function when click on radio button"}, {"ancestorTitles": ["<Radio />"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "<Radio /> must not called `onChange` function when click on disabled radio button", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "must not called `onChange` function when click on disabled radio button"}, {"ancestorTitles": ["RadioGroup"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "RadioGroup must checked on the option that have the same value with the group value", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "must checked on the option that have the same value with the group value"}, {"ancestorTitles": ["RadioGroup"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "RadioGroup must called `onChange` when clicked on an option", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "must called `onChange` when clicked on an option"}, {"ancestorTitles": ["RadioGroup"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "RadioGroup must disabled all options when the group is disabled", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "must disabled all options when the group is disabled"}], "endTime": 1730458920037, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Radio/Radio.test.tsx", "startTime": 1730458919511, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["IconButton Component"], "duration": 28, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["IconButton Component"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component applies size variants correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies size variants correctly"}, {"ancestorTitles": ["IconButton Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component renders children icon correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders children icon correctly"}, {"ancestorTitles": ["IconButton Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component applies custom className", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies custom className"}, {"ancestorTitles": ["IconButton Component"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component triggers onClick event handler when clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "triggers onClick event handler when clicked"}, {"ancestorTitles": ["IconButton Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "IconButton Component applies correct color classes based on default styles", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies correct color classes based on default styles"}], "endTime": 1730458920067, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/IconButton/IconButton.test.tsx", "startTime": 1730458919646, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["DatePicker Component"], "duration": 72, "failureDetails": [], "failureMessages": [], "fullName": "DatePicker Component rendering custom calendar: thai, locale: thai_th", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "rendering custom calendar: thai, locale: thai_th"}, {"ancestorTitles": ["DatePicker Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "DatePicker Component given 2024 in Thai's year format (equivalent to 2567), should return true for checking leap year", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "given 2024 in Thai's year format (equivalent to 2567), should return true for checking leap year"}, {"ancestorTitles": ["DatePicker Component"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "DatePicker Component given 2023 in Thai's year format (equivalent to 2566), should return false for checking leap year", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "given 2023 in Thai's year format (equivalent to 2566), should return false for checking leap year"}, {"ancestorTitles": ["DatePicker Component"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "DatePicker Component given 2028 (+4 from 2024) in Thai's year format (equivalent to 2571), should return true for checking leap year", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "given 2028 (+4 from 2024) in Thai's year format (equivalent to 2571), should return true for checking leap year"}], "endTime": 1730458920090, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/DatePicker/DatePicker.test.tsx", "startTime": 1730458919475, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["NavBar Component"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component renders Home menu item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders Home menu item"}, {"ancestorTitles": ["NavBar Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component calls onChange when a menu item is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onChange when a menu item is clicked"}, {"ancestorTitles": ["NavBar Component"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component applies active class to the active menu item", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies active class to the active menu item"}, {"ancestorTitles": ["NavBar Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component shows badge for menu items with a badge", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "shows badge for menu items with a badge"}, {"ancestorTitles": ["NavBar Component"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component applies custom className", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies custom className"}, {"ancestorTitles": ["NavBar Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component hides shadow when hideShadow is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "hides shadow when hideShadow is true"}, {"ancestorTitles": ["NavBar Component"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component displays the correct icon based on the active state", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "displays the correct icon based on the active state"}, {"ancestorTitles": ["NavBar Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NavBar Component does not render menu items with the hidden prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render menu items with the hidden prop"}], "endTime": 1730458920103, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/NavBar/NavBar.test.tsx", "startTime": 1730458919601, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["ProductCard Component"], "duration": 42, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the component with default props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders the component with default props"}, {"ancestorTitles": ["ProductCard Component"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the image when imageSrc is provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders the image when imageSrc is provided"}, {"ancestorTitles": ["ProductCard Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the default noImageBox when no imageSrc is provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders the default noImageBox when no imageSrc is provided"}, {"ancestorTitles": ["ProductCard Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the image overlay when imageOverlay is provided as string", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the image overlay when imageOverlay is provided as string"}, {"ancestorTitles": ["ProductCard Component"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the custom image overlay when imageOverlay is provided as JSX", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the custom image overlay when imageOverlay is provided as JSX"}, {"ancestorTitles": ["ProductCard Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the title when title prop is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the title when title prop is provided"}, {"ancestorTitles": ["ProductCard Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the body when body prop is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the body when body prop is provided"}, {"ancestorTitles": ["ProductCard Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the footer when footer prop is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the footer when footer prop is provided"}, {"ancestorTitles": ["ProductCard Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the product card with custom size", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the product card with custom size"}, {"ancestorTitles": ["ProductCard Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the product card with responsive size when size is set to fill", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the product card with responsive size when size is set to fill"}, {"ancestorTitles": ["ProductCard Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "ProductCard Component renders the extra content when extra prop is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders the extra content when extra prop is provided"}], "endTime": 1730458920221, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/ProductCard/ProductCard.test.tsx", "startTime": 1730458919756, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Icon Component"], "duration": 18, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["Icon Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component applies variant classes correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies variant classes correctly"}, {"ancestorTitles": ["Icon Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component applies color classes correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies color classes correctly"}, {"ancestorTitles": ["Icon Component"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component applies size classes correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies size classes correctly"}, {"ancestorTitles": ["Icon Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component renders SVG children correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "renders SVG children correctly"}, {"ancestorTitles": ["Icon Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component does not render ApolloIcons if SVG child does not match", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render ApolloIcons if SVG child does not match"}, {"ancestorTitles": ["Icon Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Icon Component supports viewBox prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "supports viewBox prop"}], "endTime": 1730458920279, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Icon/Icon.test.tsx", "startTime": 1730458919747, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["DateInput Component"], "duration": 48, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["DateInput Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component renders date in Gregorian era if era is set to 'ad'", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders date in Gregorian era if era is set to 'ad'"}, {"ancestorTitles": ["DateInput Component"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component calls onChange with selected date", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onChange with selected date"}, {"ancestorTitles": ["DateInput Component"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component updates selected date with Buddhist calendar if era is 'bd'", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "updates selected date with Buddhist calendar if era is 'bd'"}, {"ancestorTitles": ["DateInput Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component renders error styles if error prop is true", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders error styles if error prop is true"}, {"ancestorTitles": ["DateInput Component"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "DateInput Component disables input when disabled prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "disables input when disabled prop is true"}], "endTime": 1730458920436, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/DateInput/DateInput.test.tsx", "startTime": 1730458915674, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Breadcrumbs Component"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "Breadcrumbs Component renders custom separator", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders custom separator"}, {"ancestorTitles": ["Breadcrumbs Component"], "duration": 47, "failureDetails": [], "failureMessages": [], "fullName": "Breadcrumbs Component renders all items without collapsing if items fit within maxItems", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders all items without collapsing if items fit within maxItems"}, {"ancestorTitles": ["Breadcrumbs Component"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Breadcrumbs Component collapses items if items exceed maxItems", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "collapses items if items exceed maxItems"}, {"ancestorTitles": ["Breadcrumbs Component"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "Breadcrumbs Component expands collapsed items when ellipsis is clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "expands collapsed items when ellipsis is clicked"}], "endTime": 1730458920504, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Breadcrumbs/Breadcrumbs.test.tsx", "startTime": 1730458920045, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Typography Component"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["Typography Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component applies correct level", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "applies correct level"}, {"ancestorTitles": ["Typography Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component applies correct alignment", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies correct alignment"}, {"ancestorTitles": ["Typography Component"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component applies color variant", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies color variant"}, {"ancestorTitles": ["Typography Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component renders with gutterBottom spacing", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with gutterBottom spacing"}, {"ancestorTitles": ["Typography Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component applies noWrap styles", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies noWrap styles"}, {"ancestorTitles": ["Typography Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Typography Component allows passing additional className", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "allows passing additional className"}], "endTime": 1730458920557, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Typography/Typography.test.tsx", "startTime": 1730458920113, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component renders startDecorator if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders startDecorator if provided"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component renders endDecorator if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders endDecorator if provided"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "<PERSON><PERSON>mpo<PERSON> calls onClose when close button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onClose when close button is clicked"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component renders with full width if fullWidth prop is true", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with full width if fullWidth prop is true"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component applies additional custom classes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies additional custom classes"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component applies appropriate color classes based on color prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies appropriate color classes based on color prop"}, {"ancestorTitles": ["<PERSON><PERSON>mpo<PERSON>"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Alert Component does not render close button if onClose is not provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render close button if onClose is not provided"}], "endTime": 1730458920578, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Alert/Alert.test.tsx", "startTime": 1730458920075, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["CapsuleTab Component"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "CapsuleTab Component renders all tabs", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "renders all tabs"}, {"ancestorTitles": ["CapsuleTab Component"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "CapsuleTab Component calls onSelect with the correct index when a tab is clicked", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "calls onSelect with the correct index when a tab is clicked"}, {"ancestorTitles": ["CapsuleTab Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "CapsuleTab Component applies the selected class to the correct tab", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "applies the selected class to the correct tab"}], "endTime": 1730458920584, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/CapsuleTab/CapsuleTab.test.tsx", "startTime": 1730458920100, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Chip Component"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component renders with default props", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with default props"}, {"ancestorTitles": ["Chip Component"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component applies variant and color classes", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "applies variant and color classes"}, {"ancestorTitles": ["Chip Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component renders with rounded style", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders with rounded style"}, {"ancestorTitles": ["Chip Component"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component renders start decorator", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders start decorator"}, {"ancestorTitles": ["Chip Component"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component displays delete icon when onDelete is provided", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "displays delete icon when onDelete is provided"}, {"ancestorTitles": ["Chip Component"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component disables delete button when chip is disabled", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "disables delete button when chip is disabled"}, {"ancestorTitles": ["Chip Component"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component applies truncated text width if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies truncated text width if provided"}, {"ancestorTitles": ["Chip Component"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Chip Component applies disabled styles when disabled", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "applies disabled styles when disabled"}], "endTime": 1730458920624, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Chip/Chip.test.tsx", "startTime": 1730458920241, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Sidebar"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar renders Drawer with specified width", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders Drawer with specified width"}, {"ancestorTitles": ["Sidebar"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar renders header content if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders header content if provided"}, {"ancestorTitles": ["Sidebar"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar renders footer content if provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders footer content if provided"}, {"ancestorTitles": ["Sidebar"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar renders each menu section in the menus prop", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders each menu section in the menus prop"}, {"ancestorTitles": ["Sidebar"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar triggers onSelectMenu when a menu item is selected", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "triggers onSelectMenu when a menu item is selected"}, {"ancestorTitles": ["Sidebar"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar renders log out button if onLogOut is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "renders log out button if onLogOut is provided"}, {"ancestorTitles": ["Sidebar"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar calls onLogOut when log out button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onLogOut when log out button is clicked"}, {"ancestorTitles": ["Sidebar"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Sidebar does not render log out button if onLogOut is not provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "does not render log out button if onLogOut is not provided"}], "endTime": 1730458920635, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Sidebar/Sidebar.test.tsx", "startTime": 1730458920295, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["<Button />"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "<Button /> loads and displays button with primary color (default)", "invocations": 1, "location": null, "numPassingAsserts": 11, "retryReasons": [], "status": "passed", "title": "loads and displays button with primary color (default)"}, {"ancestorTitles": ["<Button />"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "<Button /> loads and displays button with danger color", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "loads and displays button with danger color"}], "endTime": 1730458920692, "message": "", "name": "/Users/<USER>/Documents/workspace/apollo-design-system/packages/ui/src/components/Button/Button.test.tsx", "startTime": 1730458920450, "status": "passed", "summary": ""}], "wasInterrupted": false}