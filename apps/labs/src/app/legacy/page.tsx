"use client"

import { useState } from "react"
import { Home, User } from "@design-systems/apollo-icons"
import {
  Accordion,
  Alert,
  Autocomplete,
  Button,
  Checkbox,
  // createTheme,
  DateInput,
  EmptyState,
  Input,
  MenuItem,
  MenuItemGroup,
  Modal,
  Sidebar,
  SidebarMenu,
  ThemeProvider,
  Typography,
} from "@design-systems/apollo-ui"

import { ComponentGroup } from "../components/common"

const options = Array.from({ length: 10 }, (_, i) => ({
  label: `Option ${i + 1}`,
  value: `value${i + 1}`,
}))

const menuConfig: SidebarMenu[] = [
  {
    key: "label",
    label: "Label",
    items: [
      {
        key: "home",
        label: "Main Menu Main MenuMain Menu",
        icon: <Home />,
        // href: "https://www.google.com",
      },
      {
        key: "dashboard",
        label: "Dasu",
        icon: <Home />,
        // href: "https://www.google.com",
      },
    ],
  },
]

export default function LegacyPage() {
  const [isOpen, setIsOpen] = useState(false)
  const [date, setDate] = useState<Date | null>(new Date())
  const [isOpenSmall, setIsOpenSmall] = useState(false)
  const [selectedMenu, setSelectedMenu] = useState<string | number>("home")
  const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["home"])

  const handleExpandStateChange = (key: string | number, expanded: boolean) => {
    setExpandedMenuKeys((prev) =>
      expanded
        ? [...prev, key as string]
        : prev.filter((prevKey) => prevKey !== key)
    )
  }
  return (
    <ThemeProvider
      scope="legacy"
      // theme={
      //   // createTheme({
      //   //   colors: {
      //   //     "apl-colors-surface-action-primary-default": "#CAA3D3",
      //   //     "apl-colors-border-primary-default": "#CAA3D3",
      //   //     "apl-colors-border-primary-subdued": "#D5B5DC",
      //   //     "apl-colors-content-primary-default": "#8C6C94",
      //   //   } as any,
      //   // }) as any
      // }
    >
      <ComponentGroup className="bg-gray-400">
        <div className="flex flex-col w-[500px] bg-white p-4 gap-2">
          <Accordion hasDivider header="Test">
            Test
          </Accordion>
          <Accordion
            borderless
            expanded
            className="w-[352px]"
            header={
              <div className="flex w-full flex-row justify-between items-center gap-2">
                <Typography level="caption" className="line-clamp-2">
                  011111 ชื่อสาขาสามารถยาว ได้้สองบรรทัด (จังหวัด)
                </Typography>
                <div className="flex flex-row justify-end items-center gap-2">
                  <Typography level="caption" className="whitespace-nowrap">
                    เปิดทำการ
                  </Typography>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation()
                      alert("Hi")
                    }}
                    variant="outline"
                  >
                    เพิ่ม
                  </Button>
                </div>
              </div>
            }
            iconPosition="start"
          >
            <ul>
              <li>
                <Typography level="caption">
                  Model: CJ SUPERMARKET + BAO CAFÉ&WASH
                </Typography>
              </li>
              <li>
                <Typography level="caption">Open date: 30/08/2004</Typography>
              </li>
              <li>
                <Typography level="caption">Close date: 30/08/2004</Typography>
              </li>
            </ul>
          </Accordion>
        </div>
        <EmptyState
          title="ยังไม่มีข้อมูล"
          description="กรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการกรุณากดปุ่มสร้างเพื่อเริ่มทำรายการ"
          name="FileNotFound"
          errorMessage="This is an error message"
        />
        <EmptyState
          title="ยังไม่มีรายการสินค้า"
          errorMessage="โปรดเพิ่มรายการสินค้าอย่างน้อย 1 รายการ"
          description="กรุณากดปุ่ม “เพิ่มสินค้า” เพื่อดำเนินการต่อ"
          name="FileNotFound"
        />
        <EmptyState
          title="Add More Information"
          description="Lorem ipsu,m"
          name="AddInformation"
          actions={
            <>
              <Button fullWidth variant="solid">
                Click Me
              </Button>
              <Button fullWidth variant="outline">
                Click Me
              </Button>
            </>
          }
        />
        <Button variant="solid" onClick={() => setIsOpen(true)}>
          Open Modal with Icon
        </Button>
        <Modal
          scrollableContent
          onOk={() => {
            console.log("[Click Event]: I'm okay")
            setIsOpen(false)
          }}
          onCancel={() => {
            console.log("[Click Event]: Canceled")
            setIsOpen(false)
          }}
          open={isOpen}
          onClose={() => {
            setIsOpen(false)
          }}
          size="full"
          maxWidth="1000px"
          header="Modal with Icon"
        >
          <div className="w-[100px] bg-amber-300 h-[2000px]">Test</div>
          <div className="text-black h-fit">
            <Autocomplete
              multiple
              label="Label"
              limitTags={2}
              placeholder="search"
              value={options}
              options={options}
            />
            This modal includes an icon. Once upon a time, there was a forest
            where plenty of birds lived and built their nests on the trees.
          </div>
        </Modal>

        <Button fullWidth>Test 2</Button>
        <div className="flex flex-col w-[600px] gap-2">
          <Button size="sm" fullWidth>
            Small
          </Button>
          <Button size="md" fullWidth>
            Medium
          </Button>
          <Button size="lg" fullWidth>
            Large
          </Button>
          <Autocomplete
            multiple
            label="Label"
            limitTags={2}
            placeholder="search"
            value={options}
            disablePortal={false}
            options={options}
          />
          <Autocomplete
            multiple
            label="Label"
            inputProps={{
              size: "small",
            }}
            value={options}
            limitTags={3}
            placeholder="search"
            hideOverflowTag
            options={options}
          />
        </div>
        <Input
          label="Small Input"
          size="small"
          placeholder="Enter text"
          disabled
        />
        <Input label="Medium Input" placeholder="Enter text" />
        <Input
          error
          label="Label"
          helperText="helperText"
          placeholder="Enter text"
        />
        <Input
          error
          label="Label"
          helperText={
            <div>
              Tetsasdas<span style={{ color: "red" }}>TTT</span>
            </div>
          }
        />
        <Checkbox label="test" />
        <Checkbox label="Disabled" checked disabled activeLabel />
        <Checkbox label="Disabled" disabled />
        <Checkbox indeterminate label="test" />
        <Checkbox disabled label="test" />
        <Checkbox checked disabled label="test" />
        <Checkbox indeterminate disabled label="test" />
        <DateInput
          value={date}
          format="dd/MM/yyyy HH:mm:ss"
          showTimeSelect
          portal
          inputProps={{
            ref: (el: HTMLInputElement | null) => {
              if (el) {
                console.log("Input Element:", el)
              }
            },
          }}
          onChange={(date) => {
            console.log("date", date)
            setDate(date)
          }}
          helperText="helperText"
          label="Label"
        />
        <DateInput
          value={date}
          minDate={new Date("2025-06-26")}
          // format="dd/MM/yyyy"
          onChange={(date) => {
            console.log("date", date)
            setDate(date)
          }}
          helperText="helperText"
          label="Label"
        />
        <>
          <Button variant="solid" onClick={() => setIsOpenSmall(true)}>
            Open Negative Modal
          </Button>
          <Modal
            // onOk={() => {
            //   console.log("[Click Event]: I'm okay")
            // }}
            open={isOpenSmall}
            onClose={() => {
              setIsOpenSmall(false)
            }}
            header="ต้องการส่งคำขอโปรโมชั่นนี้"
          >
            <div>This modal is specifically for negative confirmation.</div>
          </Modal>
        </>
        <div className="w-full h-[500px] self-stretch bg-slate-300 border border-border-default overflow-auto">
          <Sidebar
            title="Apollo"
            collapsible
            menus={menuConfig}
            selectedMenuKey={selectedMenu}
            // onSelectMenu={setSelectedMenu}
            expandedMenuKeys={expandedMenuKeys}
            // onExpandedChange={handleExpandStateChange}
            footer={
              <MenuItemGroup icon={<User />} label="Profile" selected>
                <MenuItem label="My Products" subItem />
              </MenuItemGroup>
            }
            onLogOut={() => {
              alert("Logout!")
            }}
          />
        </div>
        <div style={{ width: 300 }}>
          <Alert
            color="info"
            description="This is a basic information alert.This is a basic information alert."
            // onClose={() => {}}
            title="Information"
          />
        </div>
      </ComponentGroup>
    </ThemeProvider>
  )
}
