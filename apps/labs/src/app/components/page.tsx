"use client"

import { useState } from "react"
import { <PERSON><PERSON>, create<PERSON>hem<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Typo<PERSON> } from "@apollo/ui"

import { AccordionDemo } from "./accordion"
import { Alerts } from "./alerts"
import { AutocompletesExample } from "./autocompletes"
import { Buttons } from "./button"
import { CapsuleTabs } from "./capsule-tabs"
import { Checkboxs } from "./checkboxs"
import { Chips } from "./chips"
import { DateInputs } from "./date-inputs"
import FloatButtons from "./float-buttons"
import IconButtons from "./icon-buttons"
import { Inputs } from "./inputs"
import { ModalDemo } from "./modals"
import { Paginations } from "./pagination"
import { posAdminTheme } from "./posAdminTheme"
import { ProductCards } from "./product-card"
import { RadioDemo } from "./radio"
import { SelectDemo } from "./select"
import { SortingIconDemo } from "./sorting-icons"
import { StepIndicators } from "./step-indicators"
import { Switches } from "./switch"
import { TabsDemo } from "./tabs"
import { Textareas } from "./textareas"
import { Typographys } from "./typographys"
import { UploadBoxDemo } from "./upload-box"

const defaultTheme = createTheme()

export default function ComponentsPage() {
  const [theme, setTheme] = useState(defaultTheme)
  return (
    <ThemeProvider theme={theme}>
      <div className="grid grid-cols-3 gap-2 justify-center items-start p-4 ">
        <div className="flex flex-col justify-start items-start fixed right-0 top-0">
          <Typography>Switch Theme</Typography>
          <div className="flex flex-row gap-2 justify-center items-center">
            <Button onClick={() => setTheme(defaultTheme)}>Default</Button>
            <Button onClick={() => setTheme(posAdminTheme)}>PosAdmin</Button>
          </div>
        </div>
        <ModalDemo />
        <ProductCards />
        <DateInputs />
        <Paginations />
        <UploadBoxDemo />
        <Alerts />
        <SortingIconDemo />
        <TabsDemo />
        <SelectDemo />
        <AccordionDemo />
        <RadioDemo />
        <AutocompletesExample />
        <StepIndicators />
        <div className="flex flex-col gap-2">
          <Chips />
          <Checkboxs />
          <FloatButtons />
          <IconButtons />
          <Switches />
        </div>
        <Typographys />
        <CapsuleTabs />
        <Inputs />
        <Textareas />
        <Buttons />
      </div>
    </ThemeProvider>
  )
}
