import React from "react"
import { StepIndicator } from "@apollo/ui"
import { CheckCircle, ClockCircle } from "@design-systems/apollo-icons"

import { ComponentBox, ComponentGroup } from "./common"

export const StepIndicators = () => {
  return (
    <ComponentGroup>
      {/* Vertical Default */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Vertical - Default</h3>
        <StepIndicator
          direction="vertical"
          items={[
            {
              state: "active",
              label: "Step 1",
              badge: { label: "In Progress", color: "process" },
              title: "Account Information",
              description: "Enter your email and create a password",
            },
            {
              state: "completed",
              label: "Step 2",
              badge: { label: "Pending" },
              title: "Personal Details",
              description: "Provide your name and contact information",
            },
            {
              state: "inactive",
              label: "Step 3",
              badge: { label: "Pending" },
              title: "Verification",
              description: "Verify your email address",
            },
          ]}
        />
      </ComponentBox>

      {/* Vertical with Icons */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Vertical - With Icons</h3>
        <StepIndicator
          direction="vertical"
          items={[
            {
              state: "active",
              label: "Step 1",
              icon: <CheckCircle size={24} />,
              badge: { label: "Completed", color: "success" },
              title: "Order Placed",
              description: "Your order has been confirmed",
            },
            {
              state: "inactive",
              label: "Step 2",
              icon: <ClockCircle size={24} />,
              badge: { label: "In Progress", color: "process" },
              title: "Processing",
              description: "We're preparing your order",
            },
            {
              state: "inactive",
              label: "Step 3",
              badge: { label: "Pending" },
              title: "Shipped",
              description: "Your order is on the way",
            },
          ]}
        />
      </ComponentBox>

      {/* Horizontal */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Horizontal</h3>
        <div className="w-full">
          <StepIndicator
            direction="horizontal"
            items={[
              {
                state: "active",
                label: "Cart",
                title: "Shopping Cart",
              },
              {
                state: "inactive",
                label: "Shipping",
                title: "Shipping Info",
              },
              {
                state: "inactive",
                label: "Payment",
                title: "Payment",
              },
              {
                state: "inactive",
                label: "Review",
                title: "Review Order",
              },
            ]}
          />
        </div>
      </ComponentBox>

      {/* Simple Vertical */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Simple - Labels Only</h3>
        <StepIndicator
          direction="vertical"
          items={[
            {
              state: "active",
              label: "Step 1",
            },
            {
              state: "inactive",
              label: "Step 2",
            },
            {
              state: "inactive",
              label: "Step 3",
            },
          ]}
        />
      </ComponentBox>

      {/* Onboarding Flow Example */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Onboarding Flow</h3>
        <StepIndicator
          direction="vertical"
          items={[
            {
              state: "active",
              label: "Welcome",
              icon: <CheckCircle size={24} />,
              badge: { label: "Current", color: "process" },
              title: "Welcome to Apollo",
              description: "Let's get you started with your new account",
            },
            {
              state: "inactive",
              label: "Profile",
              title: "Complete Your Profile",
              description: "Add your photo and bio",
            },
            {
              state: "inactive",
              label: "Preferences",
              title: "Set Your Preferences",
              description: "Customize your experience",
            },
            {
              state: "inactive",
              label: "Done",
              title: "You're All Set!",
              description: "Start exploring the platform",
            },
          ]}
        />
      </ComponentBox>

      {/* Checkout Process */}
      <ComponentBox>
        <h3 className="text-lg font-semibold mb-4">Checkout Process</h3>
        <div className="w-full">
          <StepIndicator
            direction="horizontal"
            items={[
              {
                state: "active",
                label: "1",
                icon: <CheckCircle size={20} />,
                title: "Cart",
                description: "Review items",
              },
              {
                state: "active",
                label: "2",
                icon: <ClockCircle size={20} />,
                badge: { label: "Current", color: "process" },
                title: "Shipping",
                description: "Enter address",
              },
              {
                state: "inactive",
                label: "3",
                title: "Payment",
                description: "Payment details",
              },
              {
                state: "inactive",
                label: "4",
                title: "Confirm",
                description: "Review & place order",
              },
            ]}
          />
        </div>
      </ComponentBox>
    </ComponentGroup>
  )
}


