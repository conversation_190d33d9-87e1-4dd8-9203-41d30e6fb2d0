# llms.txt Proposal for Apollo Design System Documentation

## Executive Summary

This proposal outlines a strategy to implement the [llmstxt.org specification](https://llmstxt.org/) for the Apollo Design System documentation. The goal is to create LLM-friendly documentation that helps AI assistants understand and utilize the Apollo Design System components effectively.

## Current Structure Analysis

### Apollo Design System Overview

The Apollo Design System is a comprehensive UI component library with two major versions:

1. **@apollo/ui** (New Design System) - Located at `packages/apollo-ui/`

   - Modern architecture using Base UI and React 19
   - 26+ components with full Storybook documentation
   - Active development and maintenance

2. **@design-systems/apollo-ui** (Legacy Design System) - Located at `packages/ui/`
   - Still in use across multiple projects
   - Maintained for backward compatibility

### Storybook Documentation Structure

The apollo-docs app (`apps/apollo-docs/`) serves as the comprehensive documentation hub with:

- **27 apollo-ui components** with detailed stories
- Organized by categories:
  - **Inputs**: Button, Input, Checkbox, Radio, Switch, Select, Autocomplete, DateInput, Textarea, UploadBox, IconButton
  - **Layout**: Accordion, Tabs, CapsuleTab
  - **Navigation**: Breadcrumbs, Pagination
  - **Data Display**: Typography, Badge, SortingIcon
  - **Feedback**: Alert, Modal, Toast
  - **Utilities**: Portal, FloatButton, Chip
  - **Theming**: Theme customization

### Story File Structure

Each `.stories.tsx` file contains:

- Component metadata (title, category, figma links)
- Multiple story variants demonstrating use cases
- Props documentation via Storybook ArgTypes
- Usage guidelines and accessibility notes
- Code examples and best practices
- Interactive controls for live testing

## Proposed llms.txt Implementation

### 1. File Structure

```
apps/apollo-docs/
├── public/
│   └── llms.txt                    # Main llms.txt file
│   └── llms/
│       ├── components/             # Component-specific markdown docs
│       │   ├── inputs/
│       │   │   ├── button.md
│       │   │   ├── input.md
│       │   │   ├── checkbox.md
│       │   │   └── ...
│       │   ├── layout/
│       │   │   ├── accordion.md
│       │   │   ├── tabs.md
│       │   │   └── ...
│       │   ├── navigation/
│       │   ├── data-display/
│       │   ├── feedback/
│       │   └── utilities/
│       ├── foundations/
│       │   ├── design-tokens.md
│       │   ├── theming.md
│       │   └── accessibility.md
│       └── guides/
│           ├── getting-started.md
│           ├── installation.md
│           └── migration.md
└── scripts/
    └── generate-llms-txt.ts        # Automated generation script
```

### 2. Main /llms.txt Content Structure

```markdown
# Apollo Design System

> Apollo is a comprehensive React design system providing 26+ production-ready components with full accessibility support, theming capabilities, and TypeScript types. Built with Base UI and optimized for modern React applications.

The Apollo Design System offers two packages:

- **@apollo/ui** - Modern component library (recommended)
- **@design-systems/apollo-ui** - Legacy version (maintenance mode)

This documentation focuses on **@apollo/ui** components with interactive examples, comprehensive props documentation, and real-world usage patterns.

## Quick Start

- [Installation Guide](/llms/guides/installation.md): Setup and configuration
- [Getting Started](/llms/guides/getting-started.md): First steps with Apollo
- [Migration Guide](/llms/guides/migration.md): Migrate from legacy to new version

## Components

### Inputs

- [Button](/llms/components/inputs/button.md): Primary action component with variants (filled, outline, text), sizes, and colors
- [Input](/llms/components/inputs/input.md): Text input with label, helper text, decorators, and validation
- [Checkbox](/llms/components/inputs/checkbox.md): Multi-selection control with indeterminate state
- [Radio](/llms/components/inputs/radio.md): Single-selection control within radio groups
- [Switch](/llms/components/inputs/switch.md): Toggle control for binary states
- [Select](/llms/components/inputs/select.md): Dropdown selection with single/multiple options
- [Autocomplete](/llms/components/inputs/autocomplete.md): Searchable select with filtering and custom options
- [DateInput](/llms/components/inputs/dateinput.md): Date selection with locales and validation
- [Textarea](/llms/components/inputs/textarea.md): Multi-line text input with character count
- [UploadBox](/llms/components/inputs/uploadbox.md): File upload with drag-and-drop support
- [IconButton](/llms/components/inputs/iconbutton.md): Icon-only button for compact interfaces

### Layout

- [Accordion](/llms/components/layout/accordion.md): Expandable content sections with single/multiple expansion
- [Tabs](/llms/components/layout/tabs.md): Tabbed interface for content organization
- [CapsuleTab](/llms/components/layout/capsuletab.md): Pill-style tab navigation

### Navigation

- [Breadcrumbs](/llms/components/navigation/breadcrumbs.md): Hierarchical navigation with separators
- [Pagination](/llms/components/navigation/pagination.md): Page navigation for large datasets

### Data Display

- [Typography](/llms/components/data-display/typography.md): Text rendering with semantic variants
- [Badge](/llms/components/data-display/badge.md): Status indicators and counters
- [SortingIcon](/llms/components/data-display/sortingicon.md): Table column sorting indicators

### Feedback

- [Alert](/llms/components/feedback/alert.md): Contextual messages with severity levels
- [Modal](/llms/components/feedback/modal.md): Overlay dialogs for focused interactions
- [Toast](/llms/components/feedback/toast.md): Temporary notification messages

### Utilities

- [Portal](/llms/components/utilities/portal.md): Render components outside DOM hierarchy
- [FloatButton](/llms/components/utilities/floatbutton.md): Floating action button
- [Chip](/llms/components/utilities/chip.md): Compact elements for tags and filters

## Foundations

- [Design Tokens](/llms/foundations/design-tokens.md): Color, spacing, typography tokens
- [Theming](/llms/foundations/theming.md): Customization and theme creation
- [Accessibility](/llms/foundations/accessibility.md): A11y guidelines and ARIA patterns

## Optional

- [Figma Design Files](https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles): Source design specifications
- [Legacy Documentation](/llms/guides/legacy.md): @design-systems/apollo-ui reference
- [Storybook Browser](https://apollo-storybook.netlify.app): Interactive component explorer
- [Contributing Guide](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/CONTRIBUTING.md): Development setup
```

### 3. Component Markdown Template

Each component markdown file should follow this structure:

````markdown
# [Component Name]

> Brief one-line description

## Installation

```tsx
import { ComponentName } from "@apollo/ui"
```
````

## Basic Usage

```tsx
// Minimal example
<ComponentName>Content</ComponentName>
```

## Props

| Prop  | Type    | Default | Description |
| ----- | ------- | ------- | ----------- |
| prop1 | string  | -       | Description |
| prop2 | boolean | false   | Description |

## Variants

### Variant 1

Description and code example

### Variant 2

Description and code example

## Examples

### Common Use Case 1

Code example with explanation

### Common Use Case 2

Code example with explanation

## Accessibility

- ARIA attributes used
- Keyboard navigation support
- Screen reader considerations

## Best Practices

- Do's and don'ts
- Common patterns
- Performance tips

## Related Components

- Link to related components

````

### 4. Automated Generation Script

Create `apps/apollo-docs/scripts/generate-llms-txt.ts`:

```typescript
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

interface Component {
  name: string
  category: string
  description: string
  storyPath: string
}

interface StoryMetadata {
  title: string
  component: string
  parameters?: {
    docs?: {
      description?: {
        component?: string
      }
    }
  }
}

/**
 * Extract component metadata from story files
 */
async function extractComponentMetadata(): Promise<Component[]> {
  const storiesDir = path.join(__dirname, '../src/stories/apollo-ui')
  const files = await fs.readdir(storiesDir)
  const storyFiles = files.filter(f => f.endsWith('.stories.tsx'))

  const components: Component[] = []

  for (const file of storyFiles) {
    const content = await fs.readFile(path.join(storiesDir, file), 'utf-8')

    // Extract title from meta object
    const titleMatch = content.match(/title:\s*["']([^"']+)["']/)
    if (!titleMatch) continue

    const fullTitle = titleMatch[1]
    const parts = fullTitle.split('/')
    const category = parts[parts.length - 2] // e.g., "Inputs", "Layout"
    const name = parts[parts.length - 1]

    // Extract description
    const descMatch = content.match(/component:\s*["']([^"']+)["']/)
    const description = descMatch ? descMatch[1] : ''

    components.push({
      name,
      category,
      description,
      storyPath: file
    })
  }

  return components
}

/**
 * Generate markdown content for each component
 */
async function generateComponentMarkdown(component: Component): Promise<string> {
  const storyPath = path.join(__dirname, '../src/stories/apollo-ui', component.storyPath)
  const content = await fs.readFile(storyPath, 'utf-8')

  // Extract JSDoc comments, props, examples from story file
  // This is a simplified version - full implementation would parse AST

  let markdown = `# ${component.name}\n\n`
  markdown += `> ${component.description}\n\n`
  markdown += `## Installation\n\n`
  markdown += '```tsx\n'
  markdown += `import { ${component.name} } from "@apollo/ui"\n`
  markdown += '```\n\n'

  // Add more sections based on story content
  // ... (props, examples, etc.)

  return markdown
}

/**
 * Generate main llms.txt file
 */
async function generateMainLlmsTxt(components: Component[]): Promise<string> {
  // Group components by category
  const grouped = components.reduce((acc, comp) => {
    if (!acc[comp.category]) acc[comp.category] = []
    acc[comp.category].push(comp)
    return acc
  }, {} as Record<string, Component[]>)

  let content = `# Apollo Design System\n\n`
  content += `> Apollo is a comprehensive React design system...\n\n`

  // Add component sections
  for (const [category, comps] of Object.entries(grouped)) {
    content += `\n## ${category}\n\n`
    for (const comp of comps) {
      const slug = comp.name.toLowerCase().replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
      const categorySlug = category.toLowerCase().replace(/\s+/g, '-')
      content += `- [${comp.name}](/llms/components/${categorySlug}/${slug}.md): ${comp.description}\n`
    }
  }

  return content
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Generating llms.txt documentation...')

  // Extract metadata
  const components = await extractComponentMetadata()
  console.log(`📦 Found ${components.length} components`)

  // Generate main llms.txt
  const llmsTxt = await generateMainLlmsTxt(components)
  const publicDir = path.join(__dirname, '../public')
  await fs.writeFile(path.join(publicDir, 'llms.txt'), llmsTxt)
  console.log('✅ Generated /llms.txt')

  // Generate component markdown files
  const llmsDir = path.join(publicDir, 'llms/components')
  await fs.mkdir(llmsDir, { recursive: true })

  for (const component of components) {
    const markdown = await generateComponentMarkdown(component)
    const categorySlug = component.category.toLowerCase().replace(/\s+/g, '-')
    const componentDir = path.join(llmsDir, categorySlug)
    await fs.mkdir(componentDir, { recursive: true })

    const slug = component.name.toLowerCase().replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
    await fs.writeFile(path.join(componentDir, `${slug}.md`), markdown)
  }

  console.log('✅ Generated component markdown files')
  console.log('🎉 Done!')
}

main().catch(console.error)
````

## Implementation Phases

### Phase 1: Foundation Setup (Week 1)

- [ ] Create directory structure in `public/llms/`
- [ ] Write main `/llms.txt` file manually
- [ ] Create 3-5 component markdown files as templates
- [ ] Test llms.txt with LLM tools

### Phase 2: Automated Generation (Week 2)

- [ ] Build TypeScript script to parse story files
- [ ] Extract component metadata, props, and examples
- [ ] Generate markdown files automatically
- [ ] Add script to package.json scripts

### Phase 3: Enhancement (Week 3)

- [ ] Add comprehensive guides (installation, migration, etc.)
- [ ] Include design tokens documentation
- [ ] Add theming and customization guides
- [ ] Create accessibility documentation

### Phase 4: Integration & Maintenance (Week 4)

- [ ] Integrate generation into build pipeline
- [ ] Set up CI/CD to auto-generate on changes
- [ ] Add validation tests for llms.txt format
- [ ] Document maintenance procedures

## Benefits

### For LLM Assistants

1. **Quick Component Discovery**: Structured index of all 26+ components
2. **Accurate Props**: Type-safe props documentation for each component
3. **Usage Examples**: Real-world code snippets for common patterns
4. **Context Understanding**: Clear categorization and relationships

### For Developers

1. **AI-Powered Help**: LLM assistants can better answer Apollo questions
2. **Faster Onboarding**: AI can guide new developers through Apollo
3. **Code Generation**: AI can generate valid Apollo component code
4. **Documentation Search**: Efficient AI-powered documentation lookup

### For Design System Team

1. **Reduced Support**: AI handles basic questions
2. **Better Discoverability**: Components are easier to find and use
3. **Standardization**: Consistent documentation format
4. **Analytics**: Track which components need better docs

## Success Metrics

1. **Coverage**: 100% of @apollo/ui components documented
2. **Accuracy**: LLM-generated code compiles and follows best practices
3. **Freshness**: Auto-generation keeps docs in sync with code
4. **Adoption**: Developers report successful LLM-assisted development

## Tools & Integrations

Consider integrating with:

- **llms_txt2ctx**: CLI tool to expand llms.txt into LLM context
- **Cursor/Copilot**: IDE integration for component suggestions
- **ChatGPT/Claude**: Direct documentation linking
- **MCP Servers**: Model Context Protocol for VS Code

## Example Generated Content

### /llms.txt Preview

```markdown
# Apollo Design System

> Apollo is a comprehensive React design system providing 26+ production-ready components...

## Components

### Inputs

- [Button](/llms/components/inputs/button.md): Primary action component with filled, outline, and text variants
- [Input](/llms/components/inputs/input.md): Text input with label, decorators, and validation support
  ...
```

### /llms/components/inputs/button.md Preview

```markdown
# Button

> Primary action component with multiple variants, sizes, and colors

## Installation

\`\`\`tsx
import { Button } from "@apollo/ui"
\`\`\`

## Basic Usage

\`\`\`tsx
<Button variant="filled" color="primary">
Click Me
</Button>
\`\`\`

## Props

| Prop           | Type                            | Default   | Description                 |
| -------------- | ------------------------------- | --------- | --------------------------- |
| variant        | 'filled' \| 'outline' \| 'text' | 'filled'  | Visual style of button      |
| size           | 'large' \| 'small'              | 'large'   | Button size                 |
| color          | 'primary' \| 'negative'         | 'primary' | Color scheme                |
| fullWidth      | boolean                         | false     | Expands to full width       |
| startDecorator | ReactNode                       | -         | Icon or element before text |
| endDecorator   | ReactNode                       | -         | Icon or element after text  |
| href           | string                          | -         | Makes button render as link |

## Examples

### With Icon

\`\`\`tsx
<Button
startDecorator={<Download />}
variant="outline"

> Download
> </Button>
> \`\`\`

### Full Width

\`\`\`tsx
<Button fullWidth variant="filled">
Submit Form
</Button>
\`\`\`

### As Link

\`\`\`tsx
<Button href="/dashboard" variant="text">
Go to Dashboard
</Button>
\`\`\`

## Accessibility

- Uses semantic `<button>` or `<a>` elements
- Supports keyboard navigation (Enter/Space)
- Provides focus indicators
- Works with screen readers

## Best Practices

✅ **Do**

- Use primary buttons for main actions
- Limit to one primary button per section
- Use descriptive button text

❌ **Don't**

- Don't use too many button variants on one screen
- Don't make buttons too small for touch targets
- Don't use buttons for navigation (use href prop)

## Related Components

- [IconButton](/llms/components/inputs/iconbutton.md) - Icon-only variant
- [FloatButton](/llms/components/utilities/floatbutton.md) - Floating action button
```

## Next Steps

1. **Review & Approval**: Design system team reviews proposal
2. **Prototype**: Create manual version for 3-5 components
3. **Validate**: Test with LLM tools (Claude, ChatGPT, Cursor)
4. **Iterate**: Refine based on testing feedback
5. **Automate**: Build generation script
6. **Deploy**: Integrate into build pipeline
7. **Announce**: Communicate to developers

## Questions for Discussion

1. Should we include legacy (@design-systems/apollo-ui) components in llms.txt?
2. What level of detail in component markdown files? (Brief vs. comprehensive)
3. Should we generate `.md` versions of actual story pages? (per llmstxt.org spec)
4. How to handle component variants and complex examples?
5. Should we include Figma design tokens in llms.txt?

## References

- [llmstxt.org Specification](https://llmstxt.org/)
- [FastHTML Implementation Example](https://www.fastht.ml/docs/llms.txt)
- [nbdev Auto-generation](https://nbdev.fast.ai/)
- [Apollo Storybook](https://apollo-storybook.netlify.app)

---

**Prepared by**: AI Assistant  
**Date**: October 16, 2025  
**Version**: 1.0
