import { forwardRef, ReactNode, useEffect, useState } from "react"
import { Typography, TypographyVariant } from "@apollo/ui"
import { hrefTo } from "@storybook/addon-links"

import { withStaticProps } from "@/components/types/withStaticProps"

interface StorybookLinkProps {
  page: string
  children: ReactNode | ReactNode[] | string
  story?: string
  level?: TypographyVariant
  className?: string
}

const StorybookLink = forwardRef<
  HTMLElement,
  StorybookLinkProps & { levels?: TypographyVariant }
>(({ page, story = "", children, level, className }, ref) => {
  const [url, setUrl] = useState("")

  useEffect(() => {
    fetchLink()
    async function fetchLink() {
      const href = await hrefTo(page, story)
      setUrl(href)
    }
  }, [page, story])
  return (
      <a href={url} style={{ margin: 0 }}>
        <Typography
          level={level || "bodyLarge"}
          className={className}
          style={{ display: "inline", textDecoration: "underline", color: 'var(--sb-link-color)' }}
          ref={ref}
        >
          {children}
        </Typography>
      </a>
  )
})

export default withStaticProps(StorybookLink, { levels: ["bodySmall"] })
