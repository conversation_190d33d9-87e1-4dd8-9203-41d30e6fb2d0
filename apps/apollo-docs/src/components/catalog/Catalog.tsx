import React, { useMemo, useState } from "react"
import { Button, Input, Typography } from "@apollo/ui"
import { Appstore, Menu, Search } from "@design-systems/apollo-icons"

import StorybookLink from "../storybook-link/StorybookLink"

interface ComponentItem {
  title: string
  href: string
  description: string
  keywords: string[]
  component: React.ReactNode
}

interface CatalogProps {
  components?: ComponentItem[]
}

const Catalog: React.FC<CatalogProps> = ({ components }) => {
  const [searchTerm, setSearchTerm] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  const filteredComponents = useMemo(() => {
    if (!components) return []
    if (!searchTerm) return components

    return components.filter((item) => {
      const searchLower = searchTerm.toLowerCase()
      return (
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.keywords.some((keyword) =>
          keyword.toLowerCase().includes(searchLower)
        )
      )
    })
  }, [searchTerm, components])

  return (
    <div
      style={{
        margin: "0 auto",
        padding: "24px",
        border: "1px solid #e9ecef",
        borderRadius: "12px",
      }}
    >
      {/* Search Section */}
      <div
        style={{
          marginBottom: "40px",
          width: "100%",
          display: "flex",
          gap: "12px",
          alignItems: "center",
        }}
      >
        <Input
          placeholder="Search by component name..."
          value={searchTerm}
          fullWidth
          onChange={(e) => setSearchTerm(e.target.value)}
          startDecorator={<Search size={20} />}
          style={{ flex: 1 }}
        />
        <div
          style={{
            display: "flex",
            gap: "4px",
            border: "1px solid #e9ecef",
            borderRadius: "8px",
            padding: "4px",
          }}
        >
          <Button
            variant={viewMode === "grid" ? "filled" : "text"}
            onClick={() => setViewMode("grid")}
            style={{
              padding: "8px 12px",
              minWidth: "auto",
            }}
          >
            <Appstore size={20} />
          </Button>
          <Button
            variant={viewMode === "list" ? "filled" : "text"}
            onClick={() => setViewMode("list")}
            style={{
              padding: "8px 12px",
              minWidth: "auto",
            }}
          >
            <Menu size={20} />
          </Button>
        </div>
      </div>

      {/* Results Section */}
      {filteredComponents.length === 0 ? (
        <div
          style={{
            textAlign: "center",
            padding: "64px 24px",
            backgroundColor: "#f8f9fa",
            borderRadius: "12px",
            border: "1px solid #e9ecef",
          }}
        >
          <Typography
            level="headlineSmall"
            style={{ color: "#6c757d", marginBottom: "8px" }}
          >
            No components found
          </Typography>
          <Typography
            level="bodyLarge"
            style={{ color: "#6c757d", marginBottom: "16px" }}
          >
            No components match "{searchTerm}"
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#adb5bd" }}>
            Try searching for "product", "card", "price", "badge", or "tab"
          </Typography>
        </div>
      ) : viewMode === "list" ? (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          {filteredComponents.map((item, index) => (
            <div
              key={`${item.title}-${index}`}
              style={{
                padding: "12px 16px",
                border: "1px solid #e9ecef",
                borderRadius: "8px",
                backgroundColor: "#fff",
              }}
            >
              <StorybookLink page={item.href}>
                <Typography level="titleMedium" style={{ fontWeight: 700 }}>
                  {item.title}
                </Typography>
              </StorybookLink>
              <Typography
                level="bodyMedium"
                style={{
                  color: "#6c757d",
                  lineHeight: 1.4,
                  marginTop: "4px",
                }}
              >
                {item.description}
              </Typography>
            </div>
          ))}
        </div>
      ) : (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gridAutoRows: "1fr",
            gap: "24px",
          }}
        >
          {filteredComponents.map((item, index) => (
            <div
              key={`${item.title}-${index}`}
              style={{
                alignSelf: "stretch",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              {/* Component Preview */}
              {item.component && (
                <div
                  style={{
                    border: "1px solid #e9ecef",
                    borderRadius: "8px",
                    padding: "20px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    minHeight: "120px",
                    height: "100%",
                  }}
                >
                  {item.component}
                </div>
              )}
              {/* Component Header */}
              <div>
                <StorybookLink page={item.href}>
                  <Typography level="titleMedium" style={{ fontWeight: 700 }}>
                    {item.title}
                  </Typography>
                </StorybookLink>
                <Typography
                  level="bodyMedium"
                  style={{
                    color: "#6c757d",
                    lineHeight: 1.4,
                  }}
                >
                  {item.description}
                </Typography>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Catalog
