import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines, MultiplePropsTable } from "@/components"
import {
  Button,
  Typography,
  UploadBox,
  useUploadSingleFile,
  useUploadMultipleFile,
} from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"
import { File } from "@design-systems/apollo-icons"

/**
 * UploadBox component
 *
 * The UploadBox component provides a drag-and-drop file upload interface with support 
 * for single and multiple files, file type restrictions, size limits, upload progress 
 * indicators, and comprehensive validation.
 *
 * Features:
 * - Single and multiple file upload support
 * - Drag and drop functionality
 * - File type validation with customizable extensions
 * - File size validation with configurable limits
 * - Upload progress indicators
 * - Error handling and validation messages
 * - Full width layout support
 * - Integration with form fields
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/UploadBox",
  component: UploadBox,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2244-5604&m=dev",
    },
    docs: {
      description: {
        component:
          "The UploadBox component provides a comprehensive file upload interface with drag-and-drop support, validation, and progress tracking.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source 
            code={`import { UploadBox, useUploadSingleFile, useUploadMultipleFile } from "@apollo/ui"`} 
            language="tsx" 
          />
          <h2 id="uploadbox-props">Props</h2>
          <ArgTypes />
          <h2 id="uploadbox-types">Type Definitions</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "UploadBoxFileState",
                props: [
                  {
                    name: "key",
                    description: "Unique identifier for the file. Used to track individual file state.",
                    type: "string",
                    required: true,
                    defaultValue: "-",
                  },
                  {
                    name: "uploading",
                    description: "Whether the file is currently uploading. When true, the progress bar is displayed.",
                    type: "boolean",
                    required: false,
                    defaultValue: "false",
                  },
                  {
                    name: "uploadProgress",
                    description: "Progress value from 0 to 100. If not provided, the progress bar will auto-animate with random increments.",
                    type: "number",
                    required: false,
                    defaultValue: "-",
                  },
                  {
                    name: "errorMessage",
                    description: "Error message to display if the upload fails. When set, the error is shown instead of the progress bar. (legacy version only)",
                    type: "string",
                    required: false,
                    defaultValue: "-",
                  },
                  {
                    name: "uploadDescription",
                    description: "Custom description text to display during upload. Replaces the default 'Please wait while we process your file' message.",
                    type: "string",
                    required: false,
                    defaultValue: "-",
                  },
                  {
                    name: "uploadMessage",
                    description: "Custom message text to display during upload (e.g., 'Uploading', 'Processing'). Replaces the default status message.",
                    type: "string",
                    required: false,
                    defaultValue: "-",
                  },
                  {
                    name: "helperTextMessage",
                    description: "Helper text to display below the file item.",
                    type: "string",
                    required: false,
                    defaultValue: "-",
                  },
                  {
                    name: "helperTextDecorator",
                    description: "Additional content to display below the helper text.",
                    type: "ReactNode",
                    required: false,
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "UploadBoxState",
                props: [
                  {
                    name: "errors",
                    description: "Array of validation errors that occurred during file upload. Each error contains a code and message.",
                    type: "UploadBoxErrorState[]",
                    defaultValue: "[]",
                  },
                ],
              },
              {
                label: "UploadBoxErrorState",
                props: [
                  {
                    name: "code",
                    description: "Error code identifier (e.g., 'EXCEED_FILE_LIMIT', 'INVALID_FILE_TYPE', 'FILE_TOO_LARGE')",
                    type: "string",
                    required: true,
                    defaultValue: "-",
                  },
                  {
                    name: "message",
                    description: "Human-readable error message describing what went wrong.",
                    type: "string",
                    required: true,
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="uploadbox-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Always provide clear labels and helper text to guide users",
              "Set appropriate file type restrictions based on your use case",
              "Configure reasonable file size limits to prevent upload issues",
              "Use the multiple prop when users need to upload several files",
              "Provide feedback during upload progress with fileState",
              "Handle errors gracefully with clear error messages",
              "Consider using the provided hooks for easier state management",
            ]}
          />
          <h2 id="uploadbox-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              "Ensure the component is keyboard accessible for users who cannot use a mouse.",
              "Provide clear and descriptive labels for the upload box.",
              <>Use <code>multiple</code> prop to allow multiple file uploads when necessary.</>,
              <>Use <code>uploading</code> prop in <code>fileState</code> to indicate specific file upload progress.</>,
              <>Use <code>uploadProgress</code> prop in <code>fileState</code> to control progress bar value (0-100). If not provided, progress animates automatically.</>,
              <>Use <code>errorMessage</code> prop in <code>fileState</code> to indicate specific file upload error.</>,
              <>Use <code>hideUploadCloseIcon</code> prop to conditionally hide the close icon based on file state.</>,
            ]}
          />
          <h2 id="uploadbox-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The UploadBox component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".uploadBox-formControl",
                description: "Styles applied to the form control container",
                usageNotes: "Use for overall component styling and positioning",
              },
              {
                cssClassName: ".uploadBox-uploadSection",
                description: "Styles applied to the upload section wrapper",
                usageNotes: "Contains the upload button and file list",
              },
              {
                cssClassName: ".uploadBox-fileConditionContainer",
                description: "Styles applied to the file condition container",
                usageNotes: "Contains the file type and size restrictions",
              },
              {
                cssClassName: ".uploadBox-fileConditionList",
                description: "Styles applied to the list of file conditions",
                usageNotes: "Contains the individual file type and size restrictions",
              },
              {
                cssClassName: ".uploadBox-uploadButton",
                description: "Styles applied to the upload button",
                usageNotes: "Use for customizing the button appearance",
              },
              {
                cssClassName: ".uploadBox-uploadedFileList",
                description: "Styles applied to the uploaded file list container",
                usageNotes: "Use for customizing the appearance of the uploaded file list",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItem",
                description: "Styles applied to each uploaded file item",
                usageNotes: "Use for customizing the appearance of individual uploaded files",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemContent",
                description: "Styles applied to the content of an uploaded file item",
                usageNotes: "Contains the file name, delete button, and error message",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemInfo",
                description: "Styles applied to the file information section",
                usageNotes: "Contains the file name and icon",
              },
              {
                cssClassName: ".uploadBox-uploadedFileItemIcon",
                description: "Styles applied to the file icon",
                usageNotes: "Use for customizing the appearance of the file icon",
              },
              {
                cssClassName: ".uploadBox-deleteIcon",
                description: "Styles applied to the delete button icon",
                usageNotes: "Use for customizing the appearance of the delete button icon",
              },
              {
                cssClassName: ".uploadBox-uploadingText",
                description: "Styles applied to the uploading status text",
                usageNotes: "Use for customizing the appearance of the uploading status text",
              },
              {
                cssClassName: ".uploadBox-loadingIndicatorContainer",
                description: "Styles applied to the loading indicator container",
                usageNotes: "Contains the loading indicator for file uploads",
              },
              {
                cssClassName: ".uploadBox-loadingIndicator",
                description: "Styles applied to the loading indicator element",
                usageNotes: "Use for customizing the appearance of the loading indicator",
              },
            ]}
          />
          <h2 id="uploadbox-examples">Examples</h2>
          <Stories title="" />
          <h2 id="uploadbox-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 400 }}>
                      <UploadBox
                        label="Profile Picture"

                        allowedFilesExtension={["jpg", "jpeg", "png"]}
                        maxFileSizeInBytes={2 * 1024 * 1024}
                      />
                    </div>
                  ),
                  description: "Use specific file type restrictions for targeted use cases",
                },
                negative: {
                  component: (
                    <div style={{ width: 400 }}>
                      <UploadBox
                        multiple
                        label="Profile Picture"

                        fileLimit={10}
                      />
                    </div>
                  ),
                  description: "Don't use multiple upload for single-item use cases",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    label: {
      control: { type: "text" },
      description: "Label for the upload box",
      table: {
        type: { summary: "ReactNode" },
      },
    },

    disabled: {
      control: { type: "boolean" },
      description: "Whether the upload box is disabled",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    multiple: {
      control: { type: "boolean" },
      description: "Whether multiple files can be uploaded",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    required: {
      control: { type: "boolean" },
      description: "Whether file upload is required",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Whether the upload box should take full width",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    fileLimit: {
      control: { type: "number" },
      description: "Maximum number of files that can be uploaded",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "6" },
      },
    },
    maxFileSizeInBytes: {
      control: { type: "number" },
      description: "Maximum file size in bytes",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "52428800 (50MB)" },
      },
    },
    uploadButtonText: {
      control: { type: "text" },
      description: "Text displayed on the upload button",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "Upload File" },
      },
    },
    alwaysShowUploadFileSection: {
      control: { type: "boolean" },
      description: "Whether to always show the upload section even after a file has been selected in single file mode",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    allowedFilesExtension: {
      control: { type: "object" },
      description: "Array of allowed file extensions",
      table: {
        type: { summary: "string[]" },
        defaultValue: { summary: "[]" },
      },
    },
    errorMessage: {
      control: { type: "text" },
      description: "Error message displayed when file upload fails",
      table: {
        type: { summary: "string" },
      },
    },
    fileState: {
      control: { type: "object" },
      description: "State of the uploaded file(s). Contains uploading status, error messages, and optional progress value. Progress bar auto-animates if uploadProgress is not provided.",
      table: {
        type: { summary: "UploadBoxFileState | UploadBoxFileState[]" },
        defaultValue: { summary: "-" },
      },
    },
    onUpload: {
      description: "Callback when files are uploaded",
      table: {
        type: { summary: "(files: Multiple extends true ? File[] : File) => void" },
      },
    },
    onDelete: {
      description: "Callback when a file is deleted",
      table: {
        type: { summary: "Multiple extends true ? (index: number) => void : () => void" },
      },
    },
    onCancelUpload: {
      description: "Callback when file upload is canceled",
      table: {
        type: { summary: "(file: UploadBoxFile<false>, index: number) => void" },
      },
    },
    value: {
      control: false,
      description: "Uploaded file(s)",
      table: {
        type: { summary: "UploadBoxFile<Multiple, FileType>" },
        defaultValue: { summary: "-" },
      },
    },
    hideUploadCloseIcon: {
      control: { type: "boolean" },
      description: "Whether to hide the close icon on uploaded file items",
      table: {
        type: { summary: "(file: UploadBoxFileState) => boolean" },
        defaultValue: { summary: "false" },
      },
    },
  },
} satisfies Meta<typeof UploadBox>

export default meta

type Story = StoryObj<typeof UploadBox>

/** Default UploadBox for single file upload */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic single file upload with default settings. Supports JPG, PNG, and SVG files up to 5MB.",
      },
    },
  },
  args: {
    label: "Upload File",

  },
}

/** UploadBox with multiple file support */
export const MultipleFiles: Story = {
  parameters: {
    docs: {
      description: {
        story: "Multiple file upload with file limit and management capabilities.",
      },
    },
  },
  render: (args) => {
      const [files, setFiles] = useState<File[]>([])

      const handleUpload = (newFiles: File[]) => {
        setFiles((prev) => [...prev, ...newFiles])
      }

      const handleDelete = (index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
      }

      return (
        <UploadBox
          multiple
          label="Upload Multiple Files"

          value={files}
          onUpload={handleUpload}
          onDelete={handleDelete}
          fileLimit={args.fileLimit}
          allowedFilesExtension={args.allowedFilesExtension}
          maxFileSizeInBytes={args.maxFileSizeInBytes}
          disabled={args.disabled}
          required={args.required}
          fullWidth={args.fullWidth}
          uploadButtonText={args.uploadButtonText}
        />
      )
    }
}

/** UploadBox with full width layout */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "UploadBox stretches to fill the width of its container.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "100%" }}>
      <UploadBox {...args} fullWidth label="Full Width Upload" />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A side-by-side comparison of common UploadBox states for quick visual reference: default, disabled, error, and with different configurations.",
      },
    },
  },
  render: () => {
    function StatesDemo() {
      const [normalFile, setNormalFile] = useState<File | null>(null)
      const [errorFile, setErrorFile] = useState<File | null>(null)

      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "start",
          }}
        >
          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Default</Typography>
            <UploadBox
              label="Upload Document"

              value={normalFile}
              onUpload={(file) => setNormalFile(file)}
              onDelete={() => setNormalFile(null)}
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Disabled</Typography>
            <UploadBox
              label="Upload Document"

              disabled
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>Required</Typography>
            <UploadBox
              label="Upload Document"

              required
            />
          </div>

          <div>
            <Typography level="bodyLarge" style={{ marginBottom: 8 }}>With Error</Typography>
            <UploadBox
              label="Upload Document"
              value={errorFile}
              onUpload={(file) => setErrorFile(file)}
              onDelete={() => setErrorFile(null)}
              errorMessage="Upload failed due to network error"
            />
          </div>
        </div>
      )
    }
    return <StatesDemo />
  },
}

/** File type restrictions */
export const FileTypeRestrictions: Story = {
  parameters: {
    docs: {
      description: {
        story: "UploadBox with different file type restrictions for specific use cases.",
      },
    },
  },
  render: () => {
    function FileTypeDemo() {
      const [imageFiles, setImageFiles] = useState<File[]>([])
      const [documentFile, setDocumentFile] = useState<File | null>(null)

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Images Only</Typography>
            <UploadBox
              multiple
              label="Upload Images"

              value={imageFiles}
              onUpload={(files) => setImageFiles((prev) => [...prev, ...files])}
              onDelete={(index) => setImageFiles((prev) => prev.filter((_, i) => i !== index))}
              allowedFilesExtension={["jpg", "jpeg", "png", "gif"]}
              fileLimit={3}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Documents Only</Typography>
            <UploadBox
              label="Upload Document"

              value={documentFile}
              onUpload={(file) => setDocumentFile(file)}
              onDelete={() => setDocumentFile(null)}
              allowedFilesExtension={["pdf", "doc", "docx", "txt"]}
            />
          </div>
        </div>
      )
    }
    return <FileTypeDemo />
  },
}

/** Size limits demonstration */
export const SizeLimits: Story = {
  parameters: {
    docs: {
      description: {
        story: "UploadBox with different file size limitations.",
      },
    },
  },
  render: () => {
    function SizeLimitsDemo() {
      const [smallFile, setSmallFile] = useState<File | null>(null)
      const [largeFile, setLargeFile] = useState<File | null>(null)

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Small Files (1MB limit)</Typography>
            <UploadBox
              label="Upload Small File"

              value={smallFile}
              onUpload={(file) => setSmallFile(file)}
              onDelete={() => setSmallFile(null)}
              maxFileSizeInBytes={1024 * 1024} // 1MB
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>Large Files (10MB limit)</Typography>
            <UploadBox
              label="Upload Large File"

              value={largeFile}
              onUpload={(file) => setLargeFile(file)}
              onDelete={() => setLargeFile(null)}
              maxFileSizeInBytes={10 * 1024 * 1024} // 10MB
            />
          </div>
        </div>
      )
    }
    return <SizeLimitsDemo />
  },
}

/** Using the useUploadSingleFile hook */
export const WithSingleFileHook: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates using the useUploadSingleFile hook for easier state management and upload progress tracking.",
      },
    },
  },
  render: () => {
    function SingleFileHookDemo() {
      const singleFileUpload = useUploadSingleFile({
        uploadFileFn: async (file) => {
          // Simulate an upload process
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log("File uploaded:", file.name)
              resolve(file)
            }, 2000)
          })
        },
        onDelete: () => {
          console.log("File deleted")
        },
      })

      return (
        <UploadBox
          {...singleFileUpload}
          label="Upload with Hook"

          allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
        />
      )
    }
    return <SingleFileHookDemo />
  },
}

/** Using the useUploadMultipleFile hook */
export const WithMultipleFileHook: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates using the useUploadMultipleFile hook for managing multiple file uploads with progress tracking.",
      },
    },
  },
  render: () => {
    function MultipleFileHookDemo() {
      const multipleFileUpload = useUploadMultipleFile({
        uploadFileFn: async (file) => {
          // Simulate an upload process
          return new Promise((resolve) => {
            setTimeout(() => {
              console.log("File uploaded:", file.name)
              resolve(file)
            }, 1500)
          })
        },
        onDelete: (fileIndex) => {
          console.log("File deleted at index:", fileIndex)
        },
      })

      return (
        <UploadBox
          {...multipleFileUpload}
          label="Upload Multiple Files with Hook"

          allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png", "gif"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
          fileLimit={4}
        />
      )
    }
    return <MultipleFileHookDemo />
  },
}

/** Custom upload button text and descriptions */
export const CustomContent: Story = {
  parameters: {
    docs: {
      description: {
        story: "UploadBox with custom upload button text and custom description rendering.",
      },
    },
  },
  render: () => {
    function CustomContentDemo() {
      const [files, setFiles] = useState<File[]>([])

      const handleUpload = (newFiles: File[]) => {
        setFiles((prev) => [...prev, ...newFiles])
      }

      const handleDelete = (index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
      }

      const renderDescription = () => (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <div style={{ marginBottom: "8px" }}><File size={32}/></div>
          <Typography level="titleMedium" style={{ marginBottom: 8 }}>
            Drop your files here
          </Typography>
          <Typography level="bodyMedium" style={{ marginBottom: 12, color: "#666" }}>
            or click to browse from your computer
          </Typography>
          <Typography level="bodySmall" style={{ color: "#999" }}>
            Supports: PDF, DOC, XLS, PPT • Max 5MB per file
          </Typography>
        </div>
      )

      return (
        <UploadBox
          multiple
          label="Custom Upload Interface"

          value={files}
          onUpload={handleUpload}
          onDelete={handleDelete}
          renderDescription={renderDescription}
          uploadButtonText="Choose Files"
          allowedFilesExtension={["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"]}
          maxFileSizeInBytes={5 * 1024 * 1024}
          fileLimit={3}
        />
      )
    }
    return <CustomContentDemo />
  },
}

/** Form integration example */
export const FormIntegration: Story = {
  parameters: {
    docs: {
      description: {
        story: "Complete form integration with required and optional file uploads, validation, and submission handling.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [formData, setFormData] = useState({
        resume: null as File | null,
        portfolio: [] as File[],
      })
      const [submitted, setSubmitted] = useState(false)
      const [errors, setErrors] = useState<Record<string, string>>({})

      const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()

        const newErrors: Record<string, string> = {}
        if (!formData.resume) {
          newErrors.resume = "Resume is required"
        }

        setErrors(newErrors)

        if (Object.keys(newErrors).length === 0) {
          setSubmitted(true)
          setTimeout(() => setSubmitted(false), 3000)
        }
      }

      const isValid = formData.resume

      return (
        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 20,
            maxWidth: 500,
          }}
        >
          <Typography level="titleMedium">Job Application</Typography>

          <UploadBox
            label="Resume"

            required
            value={formData.resume}
            onUpload={(file) =>
              setFormData((prev) => ({ ...prev, resume: file }))
            }
            onDelete={() => setFormData((prev) => ({ ...prev, resume: null }))}
            allowedFilesExtension={["pdf", "doc", "docx"]}
            maxFileSizeInBytes={2 * 1024 * 1024} // 2MB
            errorMessage={errors.resume}
            fullWidth
          />
          <UploadBox
            multiple
            label="Portfolio (Optional)"

            value={formData.portfolio}
            onUpload={(files) =>
              setFormData((prev) => ({
                ...prev,
                portfolio: [...prev.portfolio, ...files],
              }))
            }
            onDelete={(index: number) =>
              setFormData((prev) => ({
                ...prev,
                portfolio: prev.portfolio.filter((_, i) => i !== index),
              }))
            }
            allowedFilesExtension={["pdf", "jpg", "jpeg", "png", "gif"]}
            maxFileSizeInBytes={5 * 1024 * 1024} // 5MB
            fileLimit={5}
            fullWidth
          />
          <Button
            type="submit"
            disabled={!isValid}
            style={{ marginTop: 8 }}
          >
            Submit Application
          </Button>

          {submitted && isValid && (
            <div
              style={{
                padding: 12,
                background: "#e8f5e8",
                border: "1px solid #4caf50",
                borderRadius: 4,
                fontSize: 14,
              }}
            >
              ✅ Application submitted successfully!
            </div>
          )}
        </form>
      )
    }
    return <FormDemo />
  },
}

/** Upload progress control demonstration */
export const UploadProgress: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates how to control and display upload progress using the uploadProgress prop. The progress bar updates in real-time as files are being uploaded.",
      },
    },
  },
  render: () => {
    function UploadProgressDemo() {
      const [file, setFile] = useState<File | null>(null)
      const [fileState, setFileState] = useState<any>(null)

      const handleUpload = (uploadedFile: File) => {
        setFile(uploadedFile)
        setFileState({ uploading: true, key: uploadedFile.name, uploadProgress: 0 })

        // Simulate upload progress
        let progress = 0
        const progressInterval = setInterval(() => {
          progress = Math.min(100, progress + Math.random() * 30)
          setFileState((prev: any) => ({ ...prev, uploadProgress: Math.round(progress) }))

          if (progress >= 100) {
            clearInterval(progressInterval)
            // Simulate upload completion after reaching 100%
            const timeout = setTimeout(() => {
              setFileState({ uploading: false, key: uploadedFile.name })
            }, 500)
            return () => clearTimeout(timeout)
          }
        }, 500)

        return () => clearInterval(progressInterval)
      }

      const handleDelete = () => {
        setFile(null)
        setFileState(null)
      }

      const handleCancelUpload = () => {
        setFile(null)
        setFileState(null)
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Single File Upload with Progress
            </Typography>
            <UploadBox
              label="Upload File with Progress"

              value={file}
              onUpload={handleUpload}
              onDelete={handleDelete}
              onCancelUpload={handleCancelUpload}
              fileState={fileState}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
            />
            {fileState && (
              <div style={{ marginTop: 12, padding: 12, backgroundColor: "#f5f5f5", borderRadius: 4 }}>
                <Typography level="bodySmall" style={{ marginBottom: 8, fontWeight: 600 }}>
                  {`Progress as percent: ${fileState.uploadProgress}`}
                </Typography>
              </div>
            )}
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Multiple Files Upload with Progress
            </Typography>
            <MultipleFilesProgressDemo />
          </div>
        </div>
      )
    }

    function MultipleFilesProgressDemo() {
      const [files, setFiles] = useState<File[]>([])
      const [fileStates, setFileStates] = useState<any[]>([])

      const handleUpload = (newFiles: File[]) => {
        const startIndex = files.length
        setFiles((prev) => [...prev, ...newFiles])

        // Simulate upload for each file
        newFiles.forEach((uploadedFile, idx) => {
          const fileIndex = startIndex + idx
          setFileStates((prev) => {
            const newStates = [...prev]
            newStates[fileIndex] = { uploading: true, key: uploadedFile.name, uploadProgress: 0 }
            return newStates
          })

          // Simulate upload progress
          let progress = 0
          const progressInterval = setInterval(() => {
            progress = Math.min(100, progress + Math.random() * 30)
            setFileStates((prevStates) => {
              const newStates = [...prevStates]
              newStates[fileIndex] = { ...newStates[fileIndex], uploadProgress: Math.round(progress) }
              return newStates
            })

            if (progress >= 100) {
              clearInterval(progressInterval)
              // Simulate upload completion after reaching 100%
              const timeout = setTimeout(() => {
                setFileStates((prevStates) => {
                  const newStates = [...prevStates]
                  newStates[fileIndex] = { uploading: false, key: uploadedFile.name }
                  return newStates
                })
              }, 500)
              return () => clearTimeout(timeout)
            }
          }, 500)
        })
      }

      const handleDelete = (index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
        setFileStates((prev) => prev.filter((_, i) => i !== index))
      }

      const handleCancelUpload = (_file: any, index: number) => {
        setFiles((prev) => prev.filter((_, i) => i !== index))
        setFileStates((prev) => prev.filter((_, i) => i !== index))
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <UploadBox
            multiple
            label="Upload Multiple Files with Progress"

            value={files}
            onUpload={handleUpload}
            onDelete={handleDelete}
            onCancelUpload={handleCancelUpload}
            fileState={fileStates}
            allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png", "gif"]}
            maxFileSizeInBytes={5 * 1024 * 1024}
            fileLimit={5}
          />
          {fileStates.length > 0 && (
            <div style={{ padding: 12, backgroundColor: "#f5f5f5", borderRadius: 4 }}>
              {fileStates.map((fileState: any, index: number) => (
                <div key={index} style={{ marginBottom: 8 }}>
                  <Typography level="bodySmall" style={{ marginBottom: 8, fontWeight: 600 }}>
                    {`File ${index + 1} Progress: ${fileState.uploadProgress}`}
                  </Typography>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }

    return <UploadProgressDemo />
  },
}

/** Always show upload button even after file selection */
export const AlwaysShowUploadButton: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates the alwaysShowUploadFileSection prop which keeps the upload button visible even after a file has been selected in single file mode. This allows users to replace or upload additional files.",
      },
    },
  },
  render: () => {
    function AlwaysShowUploadButtonDemo() {
      const [file, setFile] = useState<File | null>(null)

      const handleUpload = (uploadedFile: File) => {
        setFile(uploadedFile)
      }

      const handleDelete = () => {
        setFile(null)
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Without alwaysShowUploadFileSection (Default)
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Upload button disappears after file selection
            </Typography>
            <UploadBox
              label="Upload File"

              value={file}
              onUpload={handleUpload}
              onDelete={handleDelete}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              With alwaysShowUploadFileSection
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Upload button remains visible for replacing or uploading additional files
            </Typography>
            <UploadBox
              label="Upload File"

              value={file}
              onUpload={handleUpload}
              onDelete={handleDelete}
              alwaysShowUploadFileSection
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
            />
          </div>
        </div>
      )
    }
    return <AlwaysShowUploadButtonDemo />
  },
}

/** Demonstrates all UploadBoxFileState props */
export const FileStatePropsExample: Story = {
  parameters: {
    docs: {
      description: {
        story: "Comprehensive example showing all available UploadBoxFileState props including uploadDescription, uploadMessage, helperTextMessage, and helperTextDecorator.",
      },
    },
  },
  render: () => {
    function FileStatePropsDemo() {
      const [file, setFile] = useState<File | null>(null)
      const [fileState, setFileState] = useState<any>(null)

      const handleUpload = (uploadedFile: File) => {
        setFile(uploadedFile)
        // Set custom fileState with all props
        setFileState({
          key: uploadedFile.name,
          uploading: true,
          uploadProgress: 0,
          uploadDescription: "Processing your document...",
          uploadMessage: "Scanning for viruses",
          helperTextMessage: "This may take a few moments",
        })

        // Simulate upload progress
        let progress = 0
        const progressInterval = setInterval(() => {
          progress = Math.min(100, progress + Math.random() * 20)
          setFileState((prev: any) => ({
            ...prev,
            uploadProgress: Math.round(progress),
            uploadMessage:
              progress < 50
                ? "Scanning for viruses"
                : progress < 80
                  ? "Compressing file"
                  : "Finalizing upload",
          }))

          if (progress >= 100) {
            clearInterval(progressInterval)
            const timeout = setTimeout(() => {
              setFileState({
                key: uploadedFile.name,
                uploading: false,
                uploadDescription: "Upload completed successfully",
                helperTextMessage: "File is ready to use",
              })
            }, 500)
            return () => clearTimeout(timeout)
          }
        }, 500)

        return () => clearInterval(progressInterval)
      }

      const handleDelete = () => {
        setFile(null)
        setFileState(null)
      }

      const handleCancelUpload = () => {
        setFile(null)
        setFileState(null)
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Custom Upload Messages with fileState Props
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Upload a file to see custom uploadDescription, uploadMessage, and helperTextMessage in action
            </Typography>
            <UploadBox
              label="Upload Document"
              required
              value={file}
              onUpload={handleUpload}
              onDelete={handleDelete}
              onCancelUpload={handleCancelUpload}
              fileState={fileState}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
            />
          </div>

          {fileState && (
            <div style={{ padding: 12, backgroundColor: "#f5f5f5", borderRadius: 4 }}>
              <Typography level="bodySmall" style={{ marginBottom: 8, fontWeight: 600 }}>
                Current fileState:
              </Typography>
              <pre style={{ margin: 0, fontSize: 12, overflow: "auto" }}>
                {JSON.stringify(fileState, null, 2)}
              </pre>
            </div>
          )}

          <div style={{ padding: 12, backgroundColor: "#e8f4f8", borderRadius: 4 }}>
            <Typography level="bodySmall" style={{ marginBottom: 8, fontWeight: 600 }}>
              UploadBoxFileState Props Used:
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20, fontSize: 12 }}>
              <li><code>key</code> - Unique identifier for the file</li>
              <li><code>uploading</code> - Whether file is currently uploading</li>
              <li><code>uploadProgress</code> - Progress value (0-100)</li>
              <li><code>uploadDescription</code> - Custom description during upload</li>
              <li><code>uploadMessage</code> - Custom status message (changes during upload)</li>
              <li><code>helperTextMessage</code> - Helper text below the file item</li>
            </ul>
          </div>
        </div>
      )
    }
    return <FileStatePropsDemo />
  },
}

/** Demonstrates renderErrorMessage and errorMessage usage */
export const ErrorRendering: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Examples showing a fully custom error UI via renderErrorMessage and a simple string error via errorMessage.",
      },
    },
  },
  render: () => {
    function ErrorDemo() {
      const [singleFile, setSingleFile] = useState<File | null>(null)
      const [multiFiles, setMultiFiles] = useState<File[]>([])
      const [showSimpleError, setShowSimpleError] = useState(false)

      const handleMultipleUpload = (files: File[]) => {
        setMultiFiles((prev) => [...prev, ...files])
      }
      const handleMultipleDelete = (index: number) => {
        setMultiFiles((prev) => prev.filter((_, i) => i !== index))
      }

      const renderCustomErrorMessage = (state: any) => (
        <div style={{ padding: 12, background: "#fff3f3", border: "1px solid #f5c2c2", borderRadius: 8, width: "100%" }}>
          <Typography level="titleSmall" style={{ color: "#c53030", marginBottom: 6 }}>
            There were problems with your upload
          </Typography>
          {state?.errors?.length ? (
            <ul style={{ margin: 0, paddingLeft: 18, color: "#8a1f1f" }}>
              {state.errors.map((e: any, i: number) => (
                <li key={i}><Typography level="bodySmall">{e.message}</Typography></li>
              ))}
            </ul>
          ) : (
            <Typography level="bodySmall" style={{ color: "#8a1f1f" }}>
              Unknown error. Please check file type/size and try again.
            </Typography>
          )}
        </div>
      )

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Custom error UI with renderErrorMessage (validation-driven)
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Try uploading a non-PDF or more than 1 file to trigger validation errors rendered by renderErrorMessage.
            </Typography>
            <UploadBox
              multiple
              label="Upload (PDF only, max 1 file)"

              value={multiFiles}
              onUpload={handleMultipleUpload}
              onDelete={handleMultipleDelete}
              renderErrorMessage={renderCustomErrorMessage}
              errorMessage="Error"
              allowedFilesExtension={["pdf"]}
              fileLimit={1}
              maxFileSizeInBytes={2 * 1024 * 1024}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Simple string error with errorMessage
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Click the button to show a simple, built-in error alert using errorMessage.
            </Typography>
            <div style={{ marginBottom: 8 }}>
              <Button size="small" onClick={() => setShowSimpleError(true)}>Trigger errorMessage</Button>
            </div>
            <UploadBox
              label="Single Upload with errorMessage"

              value={singleFile}
              alwaysShowUploadFileSection
              errorOnClose={() => setShowSimpleError(false)}
              onDelete={() => setSingleFile(null)}
              onUpload={(file) => setSingleFile(file)}
              errorMessage={showSimpleError ? "Something went wrong while uploading. Please retry." : undefined}
            />
          </div>
        </div>
      )
    }
    return <ErrorDemo />
  },
}

/** Demonstrates hideUploadCloseIcon prop functionality */
export const HideCloseIconExample: Story = {
  parameters: {
    docs: {
      description: {
        story: "Shows how to use the hideUploadCloseIcon prop to conditionally hide the close icon based on file state. This is useful for preventing file removal during certain states or conditions.",
      },
    },
  },
  render: () => {
    function HideCloseIconDemo() {
      const [file, setFile] = useState<File | null>(null)
      const [fileState, setFileState] = useState<any>(null)
      const [multipleFiles, setMultipleFiles] = useState<File[]>([])
      const [multipleFileStates, setMultipleFileStates] = useState<any[]>([])

      // Single file upload handler
      const handleSingleUpload = (uploadedFile: File) => {
        setFile(uploadedFile)
        setFileState({
          key: uploadedFile.name,
          uploading: true,
          uploadProgress: 0,
        })

        let progress = 0
        const progressInterval = setInterval(() => {
          progress = Math.min(100, progress + Math.random() * 25)
          setFileState((prev: any) => ({
            ...prev,
            uploadProgress: Math.round(progress),
          }))

          if (progress >= 100) {
            clearInterval(progressInterval)
            const timeout = setTimeout(() => {
              setFileState({
                key: uploadedFile.name,
                uploading: false,
              })
            }, 500)
            return () => clearTimeout(timeout)
          }
        }, 500)

        return () => clearInterval(progressInterval)
      }

      // Multiple files upload handler
      const handleMultipleUpload = (newFiles: File[]) => {
        const startIndex = multipleFiles.length
        setMultipleFiles((prev) => [...prev, ...newFiles])

        newFiles.forEach((uploadedFile, idx) => {
          const fileIndex = startIndex + idx
          setMultipleFileStates((prev) => {
            const newStates = [...prev]
            newStates[fileIndex] = { uploading: true, key: uploadedFile.name, uploadProgress: 0 }
            return newStates
          })

          let progress = 0
          const progressInterval = setInterval(() => {
            progress = Math.min(100, progress + Math.random() * 30)
            setMultipleFileStates((prevStates) => {
              const newStates = [...prevStates]
              newStates[fileIndex] = { ...newStates[fileIndex], uploadProgress: Math.round(progress) }
              return newStates
            })

            if (progress >= 100) {
              clearInterval(progressInterval)
              const timeout = setTimeout(() => {
                setMultipleFileStates((prevStates) => {
                  const newStates = [...prevStates]
                  newStates[fileIndex] = { uploading: false, key: uploadedFile.name }
                  return newStates
                })
              }, 500)
              return () => clearTimeout(timeout)
            }
          }, 500)
        })
      }

      const handleSingleDelete = () => {
        setFile(null)
        setFileState(null)
      }

      const handleMultipleDelete = (index: number) => {
        setMultipleFiles((prev) => prev.filter((_, i) => i !== index))
        setMultipleFileStates((prev) => prev.filter((_, i) => i !== index))
      }

      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 24 }}>
          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Hide Close Icon During Upload
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Close icon is hidden while file is uploading, shown after upload completes
            </Typography>
            <UploadBox
              label="Upload File"

              value={file}
              onUpload={handleSingleUpload}
              onDelete={handleSingleDelete}
              fileState={fileState}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
              hideUploadCloseIcon={(state) => state?.uploading ?? false}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Hide Close Icon Based on Progress
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Close icon is hidden until upload reaches 100% progress
            </Typography>
            <UploadBox
              label="Upload File"

              value={file}
              onUpload={handleSingleUpload}
              onDelete={handleSingleDelete}
              fileState={fileState}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
              hideUploadCloseIcon={(state) => (state?.uploadProgress ?? 0) < 100}
            />
          </div>

          <div>
            <Typography level="titleSmall" style={{ marginBottom: 8 }}>
              Multiple Files - Hide Close Icon During Upload
            </Typography>
            <Typography level="bodySmall" style={{ marginBottom: 12, color: "#666" }}>
              Each file's close icon is hidden while uploading
            </Typography>
            <UploadBox
              multiple
              label="Upload Multiple Files"

              value={multipleFiles}
              onUpload={handleMultipleUpload}
              onDelete={handleMultipleDelete}
              fileState={multipleFileStates}
              allowedFilesExtension={["pdf", "doc", "docx", "jpg", "png"]}
              maxFileSizeInBytes={5 * 1024 * 1024}
              fileLimit={5}
              hideUploadCloseIcon={(state) => state?.uploading ?? false}
            />
          </div>

          <div style={{ padding: 12, backgroundColor: "#e8f4f8", borderRadius: 4 }}>
            <Typography level="bodySmall" style={{ marginBottom: 8, fontWeight: 600 }}>
              hideUploadCloseIcon Prop Examples:
            </Typography>
            <ul style={{ margin: 0, paddingLeft: 20, fontSize: 12 }}>
              <li><code>hideUploadCloseIcon=&#123;(state) =&gt; state?.uploading ?? false&#125;</code> - Hide during upload</li>
              <li><code>hideUploadCloseIcon=&#123;(state) =&gt; (state?.uploadProgress ?? 0) &lt; 100&#125;</code> - Hide until 100% progress</li>
              <li><code>hideUploadCloseIcon=&#123;(state) =&gt; state?.error ?? false&#125;</code> - Hide on error</li>
              <li><code>hideUploadCloseIcon=&#123;() =&gt; true&#125;</code> - Always hide close icon</li>
            </ul>
          </div>
        </div>
      )
    }
    return <HideCloseIconDemo />
  },
}