import { DiffCodeBlock } from "../../standalone-document/migration-guide/DiffCodeBlock"

import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕ui/Switch to UI Styles of V1" tags={["docs"]} />

# Switch to UI Styles of V1

If you don't want the style to change after upgrade, we have provided a `legacy layer` rule that can restore v1 (legacy) style.

you can learn more about legacy and another layers in [CSS Layers](?path=/docs/apollo∕ui-theming-css-layers--docs)

## Usage

### Step 1: Legacy Theme Setup from @apollo/ui

Apollo legacy components require a theme provider to work correctly. Wrap your app with the `ThemeProvider` from `@apollo/ui` and create a theme:

```jsx title="App.js"
import React from "react"
import {
  ApolloProvider,
  createTheme as createLegacyTheme,
  ThemeProvider as LegacyThemeProvider,
} from "@apollo/ui"

function App({ children }) {
  return (
    <LegacyThemeProvider theme={createLegacyTheme()}>
      <ApolloProvider>
        {children}
      </ApolloProvider>
    </LegacyThemeProvider>
  )
}
```

### Step 2: Declare Your Layers

Add this at the top of your main CSS file:

```css
@layer reset, base, apollo, legacy;
```

If you need to customize the legacy style only, you can Wrap all your existing legacy styles in the legacy layer:

```css
@layer legacy {
  /* All your existing styles go here */
}
```

## Convert to Default Layers

If you no longer wish to use the legacy style, you may remove the layer declarations. However, if you intend to use CSS layers, you should define the default layers explicitly.

<DiffCodeBlock
  code={`- @layer reset, base, apollo, legacy;
+ @layer reset, base, legacy, apollo;`}
/>
