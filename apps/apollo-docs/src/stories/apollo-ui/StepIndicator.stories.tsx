import React from "react"
import {
  ComponentRules,
  CSSClassesTable,
  MultiplePropsTable,
  UsageGuidelines,
} from "@/components"
import { Button, StepIndicator } from "@apollo/ui"
import {
  CheckCircle,
  ClockCircle,
  InfoCircle,
} from "@design-systems/apollo-icons"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * StepIndicator component
 *
 * The StepIndicator component displays a sequence of steps in a process,
 * showing progress through active and inactive states. It supports both
 * vertical and horizontal layouts with customizable content for each step.
 *
 * Notes:
 * - Default direction is "vertical"
 * - Each step can have a state of "active" or "inactive"
 * - Supports labels, titles, descriptions, badges, and custom icons
 * - Steps are connected with visual lines
 * - Fully responsive and accessible
 */
const meta = {
  title: "@apollo∕ui/Components/Navigation/StepIndicator",
  component: StepIndicator,
  tags: ["autodocs"],
  argTypes: {
    items: {
      control: { type: "object" },
      description: "Array of step items to display",
      table: {
        type: { summary: "StepIndicatorItem[]" },
      },
    },
    direction: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
      description: "Layout direction for the step indicator",
      table: {
        type: { summary: '"horizontal" | "vertical"' },
        defaultValue: { summary: '"vertical"' },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class name",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "undefined" },
      },
    },
  },
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The StepIndicator component provides a visual representation of multi-step processes or workflows. It helps users understand their current position in a sequence and what steps remain.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { StepIndicator } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="step-indicator-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "StepIndicator",
                props: [
                  {
                    name: "items",
                    description: "Array of step items to display",
                    type: "StepIndicatorItem[]",
                    required: true,
                    defaultValue: "-",
                  },
                  {
                    name: "direction",
                    description: "Layout direction for the step indicator",
                    type: '"horizontal" | "vertical"',
                    defaultValue: '"vertical"',
                  },
                  {
                    name: "className",
                    description: "Additional CSS class name",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "StepIndicatorItem",
                props: [
                  {
                    name: "state",
                    description: "The current state of the step",
                    type: '"active" | "inactive" | "completed"',
                    required: true,
                    defaultValue: "-",
                  },
                  {
                    name: "label",
                    description: "Optional label text for the step",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "icon",
                    description: "Optional icon to display in the indicator",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "badge",
                    description:
                      "Optional badge configuration with label, color, and icon",
                    type: 'Pick<BadgeProps, "label" | "color" | "icon">',
                    defaultValue: "-",
                  },
                  {
                    name: "title",
                    description: "Optional title text for the step content",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "description",
                    description:
                      "Optional description text for the step content",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "renderItem",
                    description:
                      "Custom render function for the entire step item",
                    type: '(props: { state: "active" | "inactive" | "completed" }) => ReactNode',
                    defaultValue: "-",
                  },
                  {
                    name: "renderLabel",
                    description: "Custom render function for the step label",
                    type: '(props: { state: "active" | "inactive" | "completed", label: string }) => ReactNode',
                    defaultValue: "-",
                  },
                  {
                    name: "renderIndicator",
                    description:
                      "Custom render function for the step indicator",
                    type: '(props: { state: "active" | "inactive" | "completed", icon: ReactNode }) => ReactNode',
                    defaultValue: "-",
                  },
                  {
                    name: "renderContent",
                    description: "Custom render function for the step content",
                    type: '(props: { state: "active" | "inactive" | "completed" }) => ReactNode',
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="step-indicator-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The StepIndicator component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloStepIndicator-root",
                description:
                  "Styles applied to the root step indicator container",
                usageNotes:
                  "Use for overall step indicator styling, direction control, and spacing between steps",
              },
              {
                cssClassName: ".ApolloStepIndicator-item",
                description:
                  "Styles applied to each individual step item container",
                usageNotes:
                  "Use for step item layout, spacing between label/indicator/content, and state-based styling",
              },
              {
                cssClassName: ".ApolloStepIndicator-label",
                description: "Styles applied to the step label element",
                usageNotes:
                  "Use for customizing step label typography and positioning",
              },
              {
                cssClassName: ".ApolloStepIndicator-indicator",
                description:
                  "Styles applied to the step indicator (icon/pointer) element",
                usageNotes:
                  "Use for indicator styling, active/inactive states, and connector line positioning",
              },
              {
                cssClassName: ".ApolloStepIndicator-indicatorIcon",
                description:
                  "Styles applied to the step indicator icon element",
                usageNotes:
                  "Use for customizing the appearance of the step indicator icon",
              },
              {
                cssClassName: ".ApolloStepIndicator-indicatorLine",
                description:
                  "Styles applied to the connector line between steps",
                usageNotes:
                  "Use for customizing the visual connection between steps in both vertical and horizontal layouts",
              },
              {
                cssClassName: ".ApolloStepIndicator-content",
                description: "Styles applied to the step content container",
                usageNotes:
                  "Use for customizing the appearance of step titles, descriptions, and badges",
              },
            ]}
          />
          <h2 id="step-indicator-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use clear, descriptive labels for each step that indicate what the user needs to do",
              "Keep the number of steps reasonable (typically 3-7 steps) to avoid overwhelming users",
              "Use the active state to clearly indicate the current step in the process",
              "Provide helpful titles and descriptions to guide users through each step",
              "Use icons sparingly and only when they add meaningful context",
              "Consider using vertical layout for mobile devices and horizontal for desktop",
              "Ensure step labels are concise but descriptive enough to be understood at a glance",
            ]}
          />
          <h2 id="step-indicator-examples">Examples</h2>
          <Stories title="" />
          <h2 id="step-indicator-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        width: 400,
                        display: "flex",
                        justifyContent: "center",
                      }}
                    >
                      <StepIndicator
                        direction="vertical"
                        items={[
                          {
                            state: "active",
                            label: "Step 1",
                            title: "Account Setup",
                            description: "Create your account and verify email",
                          },
                          {
                            state: "inactive",
                            label: "Step 2",
                            title: "Profile Information",
                            description: "Add your personal details",
                          },
                          {
                            state: "inactive",
                            label: "Step 3",
                            title: "Preferences",
                            description: "Customize your experience",
                          },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Use clear, sequential labels and descriptive titles that guide users through the process",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        width: 400,
                        display: "flex",
                        justifyContent: "center",
                      }}
                    >
                      <StepIndicator
                        direction="vertical"
                        items={[
                          {
                            state: "active",
                            label: "1",
                            title: "Do this",
                          },
                          {
                            state: "inactive",
                            label: "2",
                            title: "Then this",
                          },
                          {
                            state: "inactive",
                            label: "3",
                            title: "Finally this",
                          },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Avoid vague labels and titles that don't clearly communicate what each step involves",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        width: 400,
                        display: "flex",
                        justifyContent: "center",
                      }}
                    >
                      <StepIndicator
                        direction="vertical"
                        items={[
                          {
                            state: "active",
                            label: "Current",
                            icon: <CheckCircle size={20} />,
                            badge: { label: "In Progress", color: "process" },
                            title: "Payment Details",
                            description: "Enter your payment information",
                          },
                          {
                            state: "inactive",
                            label: "Next",
                            title: "Review Order",
                            description: "Confirm your purchase",
                          },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Use icons and badges to provide additional context and status information",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        width: 400,
                        display: "flex",
                        justifyContent: "center",
                      }}
                    >
                      <StepIndicator
                        direction="vertical"
                        items={[
                          {
                            state: "active",
                            label: "Step 1",
                            icon: <CheckCircle size={20} />,
                            badge: { label: "Status" },
                            title: "Step One Title Text",
                          },
                          {
                            state: "inactive",
                            label: "Step 2",
                            icon: <ClockCircle size={20} />,
                            badge: { label: "Status" },
                            title: "Step Two Title Text",
                          },
                          {
                            state: "inactive",
                            label: "Step 3",
                            icon: <InfoCircle size={20} />,
                            badge: { label: "Status" },
                            title: "Step Three Title Text",
                          },
                        ]}
                      />
                    </div>
                  ),
                  description:
                    "Avoid overusing icons and badges on every step when they don't add meaningful information",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
} satisfies Meta<typeof StepIndicator>

export default meta
type Story = StoryObj<typeof meta>

export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic vertical step indicator showing a multi-step form process. Displays three steps with different states: active (current step), completed (finished steps), and inactive (pending steps). Each step includes a label, badge, title, and description.",
      },
    },
  },
  args: {
    direction: "vertical",
    items: [
      {
        state: "active",
        label: "Step 1",
        badge: { label: "In Progress", color: "process" },
        title: "Account Information",
        description: "Enter your email and create a password",
      },
      {
        state: "completed",
        label: "Step 2",
        badge: { label: "Pending" },
        title: "Personal Details",
        description: "Provide your name and contact information",
      },
      {
        state: "inactive",
        label: "Step 3",
        title: "Verification",
      },
    ],
  },
}

export const WithIcons: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Step indicator with custom icons for each step. Demonstrates how to use icons (CheckCircle, ClockCircle) to provide visual context for different step states. Useful for order tracking, delivery status, or any process where visual icons enhance understanding.",
      },
    },
  },
  args: {
    direction: "vertical",
    items: [
      {
        state: "active",
        label: "Step 1",
        icon: <CheckCircle size={24} />,
        badge: { label: "Completed", color: "success" },
        title: "Order Placed",
        description: "Your order has been confirmed",
      },
      {
        state: "completed",
        label: "Step 2",
        icon: <ClockCircle size={24} />,
        badge: { label: "In Progress", color: "process" },
        title: "Processing",
        description: "We're preparing your order",
      },
      {
        state: "inactive",
        label: "Step 3",
        badge: { label: "Pending" },
        title: "Shipped",
        description: "Your order is on the way",
      },
    ],
  },
}

export const Horizontal: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Horizontal layout step indicator ideal for checkout flows and wizards. Steps are displayed side-by-side with connecting lines.",
      },
    },
  },
  render: (args) => {
    return (
      <div style={{ width: "100%", margin: "0 auto", maxWidth: 600 }}>
        <StepIndicator {...args} />
      </div>
    )
  },
  args: {
    direction: "horizontal",
    items: [
      {
        state: "active",
        label: "Cart",
        title: "Shopping Cart",
      },
      {
        state: "inactive",
        label: "Shipping",
        title: "Shipping Info",
      },
      {
        state: "inactive",
        label: "Payment",
        title: "Payment",
      },
      {
        state: "inactive",
        label: "Review",
        title: "Review Order",
      },
    ],
  },
}

export const RealWorldExample: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Real-world activity timeline example showing a document approval workflow. Displays a vertical timeline with dates, status badges, user information, and descriptions. Perfect for audit trails, activity logs, and process history tracking. Features mixed states (active, completed) with color-coded badges.",
      },
    },
  },
  args: {
    style: {
      rowGap: "var(--apl-alias-spacing-gap-gap5, 8px)",
    },
    direction: "vertical",
    items: [
      {
        state: "active",
        label: "17 Sep",
        badge: { label: "กำลังตรวจสอบ", color: "process" },
        title: "Update by: kunlanut.sri",
        description: "พบข้อต้องสงสัย",
      },
      {
        state: "completed",
        label: "16 Sep",
        badge: { label: "มอบหมายตรวจสอบ", color: "warning" },
        title: "Update by: kunlanut.sri",
        description: "ไม่พบการทุจริต",
      },
      {
        state: "completed",
        label: "16 Sep",
        badge: { label: "รอมอบหมายตรวจสอบ", color: "error" },
        title: "Update by: kunlanut.sri",
        description: "ไม่พบการทุจริต",
      },
      {
        state: "completed",
        label: "15 Sep",
        badge: { label: "เบาะแสล่าสุด", color: "warning" },
        title: "Create by: sarakard.pas",
      },
    ],
  },
}

export const SwitchStep: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Interactive horizontal stepper with dynamic step switching. Demonstrates a fully functional multi-step wizard with Next/Previous buttons. Icons change color based on step state (completed, active, inactive). Perfect for form wizards, onboarding flows, and approval workflows. Features dynamic icon coloring and state management.",
      },
    },
  },
  render: () => {
    const [currentStep, setCurrentStep] = React.useState(0)
    const [items, setItems] = React.useState()

    const steps = [
      {
        key: "1",
        badge: { label: "อนุมัติ", color: "success" },
      },
      {
        key: "2",
        badge: { label: "รออนุมัติ AM", color: "warning" },
      },
      {
        key: "3",
        badge: { label: "อนุมัติ OC", color: "success" },
      },
      {
        key: "4",
        badge: { label: "ปิดงาน", color: "success" },
      },
    ]

    const getIconColor = (key, state) => {
      if (key === "2" && (state === "completed" || state === "active"))
        return "var(--apl-alias-color-warning-warning, #D9AF09)"
      if (state === "completed" || state === "active")
        return "var(--apl-alias-color-primary-primary, #016E2E)"
      return undefined
    }

    React.useEffect(() => {
      const newItems = steps.map((step, index) => {
        const state =
          index < currentStep
            ? "completed"
            : index === currentStep
              ? "active"
              : "inactive"
        return {
          ...step,
          state,
          ...(state === "inactive"
            ? {}
            : {
                icon:
                  step.key === "2" ? (
                    <ClockCircle
                      size={24}
                      style={{ color: getIconColor(step.key, state) }}
                    />
                  ) : (
                    <CheckCircle
                      size={24}
                      style={{ color: getIconColor(step.key, state) }}
                    />
                  ),
              }),
        }
      })
      setItems(newItems)
    }, [currentStep])

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
        <StepIndicator direction="horizontal" items={items} />
        <div
          style={{
            padding: "40px",
            border: "2px dashed #ccc",
            borderRadius: "8px",
            textAlign: "center",
            color: "#999",
            minHeight: "200px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {steps[currentStep].title || `Step ${currentStep + 1} content`}
        </div>
        <div
          style={{
            display: "flex",
            gap: "12px",
            justifyContent: "space-between",
          }}
        >
          <Button
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
            variant="outline"
          >
            Previous
          </Button>
          <Button
            onClick={() =>
              setCurrentStep(Math.min(steps.length - 1, currentStep + 1))
            }
            disabled={currentStep === steps.length - 1}
          >
            Next
          </Button>
        </div>
      </div>
    )
  },
}

export const WithCustomRender: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Advanced example demonstrating custom render functions for complete control over step rendering. Shows how to customize the indicator and content areas with custom styling, borders, and backgrounds. Useful for creating unique step designs that match specific brand requirements.",
      },
    },
  },
  render: () => {
    const items = [
      {
        state: "active",
        label: "Step 1",
        title: "Custom Rendered Step",
        description: "This step uses custom render functions",
        renderIndicator: ({ state, icon }) => (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
              height: "100%",
              flexDirection: "column",
            }}
          >
            <div
              style={{
                borderRadius: "50%",
                height: "24px",
                width: "24px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "var(--apl-alias-color-primary-primary)",
              }}
            >
              <CheckCircle size={24} />
            </div>
            <div
              style={{
                width: "1px",
                height: "100%",
                backgroundColor: "var(--apl-alias-color-primary-primary)",
              }}
            />
          </div>
        ),
        renderContent: ({ state }) => (
          <div
            style={{
              padding: "12px",
              border: `2px solid ${state === "active" ? "#016E2E" : "#C8C6C6"}`,
              borderRadius: "8px",
              backgroundColor: state === "active" ? "#f0f9f4" : "#f9f9f9",
            }}
          >
            <strong>Custom Content</strong>
            <p>This content is rendered with a custom function</p>
          </div>
        ),
      },
      {
        state: "inactive",
        label: "Step 2",
        title: "Regular Step",
        description: "This step uses default rendering",
      },
      {
        state: "inactive",
        label: "Step 3",
        title: "Another Step",
        description: "Also uses default rendering",
      },
    ]

    return <StepIndicator direction="vertical" items={items} />
  },
}
