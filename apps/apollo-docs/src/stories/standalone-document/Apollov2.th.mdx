import { Accordion, Alert, Typography } from "@apollo/ui"

<Alert
  type="information"
  fullWidth
  description="บทความนี้จะอธิบายเกี่ยวกับสิ่งที่จะเปลี่ยนแปลง และเกิดขึ้นหลังจากนี้เกี่ยวกับ Design System ครับ"
/>

# เกริ่นนิดนึง

ก่อนอื่นเลย ให้ทุกคนอ่านคำนี้ไว้นะครับ

## "ของเก่าใช้ได้ ของใหม่ไปต่อ"

### หมายความว่า

**@design-systems/apollo-ui (v1)**

- จะยังคงใช้งานได้ตามปกติ
- รวมถึงจะยังคงมีการแก้ไขบัคต่างๆ ต่อไป
- แต่ไม่มีการเพิ่มฟีเจอร์ใหม่ๆแล้ว

**@apollo/ui (v2)**

- จะเป็นเวอร์ชั่นที่เราจะพัฒนาและเพิ่มฟีเจอร์ใหม่ๆ ต่อไปในอนาคต
- ทำให้ stable มากที่สุด เพื่อให้ Developer ทำงานได้ไวขึ้น

<Alert
  type="warning"
  fullWidth
  description="⚠️ สรุปคือ: ไม่ต้องลบ v1 ออกไป เพียงแค่ install v2 เพิ่มเติมเข้ามาเท่านั้น"
/>

<br />

---

## ⚙️ ก่อนหน้านี้ใน v1 เกิดอะไรขึ้นบ้าง

### Feedback ที่เราได้รับจาก Developer

นี่คือ Feedback ที่เราได้รับจาก Developer หลายๆ คนที่ใช้งาน Apollo v1

- 😞 เวลาใช้งาน Apollo v1 ทำ UI ช้าลงแทนที่จะไวขึ้น
- 😞 ตัว Component เอง Design ไม่ตรงกับ Figma ทำให้ต้อง Customize ให้ Component
- 😞 อยากอัพเดทเวอร์ชั่น React แต่ทำไม่ได้เพราะ Library เก่าเกินไป (รวมถึง Apollo)
- 😞 ปัญหายิบย่อยอื่นๆ น่าหงุดหงิด

### ปัญหาทางเทคนิค

- ไม่สามารถใช้กับ React เวอร์ชั่นเก่าๆ หรือใหม่เกินไปได้
- บังคับให้ Project ที่นำไปใช้งานต้องใช้ React 18.2 ขึ้นไป
- ต้องมี Tailwind CSS เป็น Library หลักในการ Styling

<br />

---

## 🚀 Library ที่ชื่อว่า @apollo/ui (v2)

### สิ่งที่เปลี่ยนแปลงหลักๆ

#### 🎨 ไม่ผูกติดกับ Tailwind CSS อีกต่อไป

- ใช้ CSS Modules แทนในฝั่ง Library (Project แค่ import CSS แล้วสามารถใช้งานได้เลย)
- ในแต่ละ Project มีอิสระในการเลือกใช้ Library สำหรับการ Styling ได้เอง (เรามี Design Token ไว้ให้เรียกใช้)

#### 🖼️ Theming ใช้งานง่ายขึ้น (M3 Inspired)

- รองรับ Dark/Light Mode
- รองรับ Nested Theme ทำให้สามารถทำ Theme ในระดับหน้าได้ (กรณีตัวอย่าง เช่น Campaign Page ที่มี Theme เป็นของตัวเอง)

#### 🏭 เปลี่ยน Bundler จาก Rollup เป็น Vite + React Compiler

- ทำให้ใช้งานกับ React 17 ไปจนถึง 19+ ได้ (ซึ่งของเก่าใช้ได้เพียง React 18.x เท่านั้น)
- ลดการใช้ Plugins ที่ไม่จำเป็นลงทำให้ Bundle ได้เร็วขึ้น

#### ⚡ Props API มีความสม่ำเสมอมากขึ้น

- Props API ของ Component หลายๆ ตัวถูกปรับปรุงให้มี Consistency

#### ✅ Design ตรงกับ Figma มากขึ้น

- ใน v2 เราได้เปลี่ยน Process การทำงานร่วมกับ Designer ใหม่
- ทำให้ Design ที่ออกมานั้นตรงกับ Figma มากขึ้น

#### ✨ Component ใหม่ๆ

- Component ใหม่ๆ ที่จะถูกเพิ่มเข้ามาในอนาคต เช่น DataTable (Rich Features)
- เพื่อให้ Developer ทำงานได้ไวขึ้น

#### ✨ เชื่อมกับ Figma Code Connect (Coming Soon)

- ในยุคที่ AI กำลังมาแรง Figma ได้มีสิ่งที่เรียกว่า MCP Server
- AI สามารถมาดึกตัวอย่างโค้ดจาก Figma มาใช้ได้เลย
- ลดการเปิด Documentation เพื่อทำความเข้าใจเกี่ยวกับ Component ต่างๆ ลง

<br />

---

## 📌 สิ่งที่เกิดขึ้นต่อจากนี้

<Alert
  type="error"
  fullWidth
  description="กรณีเกิดบัคขึ้น จากปัจจุบันที่เราแก้ไขตาม Priority ของงานซึ่งรวมถึงงานอื่นๆของเรา นอกเหนือจาก Design System

เป็น ทุกบัคที่พบใน v2 เราจะมองว่าเป็น URGENT Bug ต้องแก้ในทันที เพื่อให้การใช้งานเป็นไปอย่างราบรื่นที่สุด"
/>

<br />

### เชิญชวนให้ติดตั้ง v2

เราขอเชิญชวนทุกคนให้ install `@apollo/ui` (v2) เพิ่มเติมเข้ามาใน Project และใช้งานต่อจากนี้ไปเท่าที่จะเป็นไปได้

โดย UX/UI Designer (Figma) และฝั่ง UI-Engineer (React Library) ได้มีการปรับ Process และกำหนดกฏเกณฑ์ใหม่ขึ้นมาเพื่อให้ UI ตรงกันเสมอ

**หากพบ UI ที่ไม่ตรงกับ Figma สามารถแจ้งได้เลยครับ**

<br />

### วิธีดูว่าไฟล์ Figma เป็น Version ไหน

ในฝั่ง Figma จะมีส่วนที่บอกว่าไฟล์นั้นๆ เป็น Version ไหน

**Page Level Badge:**

<img
  style={{ maxWidth: "300px" }}
  src="/v2-article/page-level-badge.png"
  alt="Page Level Badge"
/>

**หรือ Part Level Badge:**

<img
  style={{ maxWidth: "300px" }}
  src="/v2-article/part-level-badge.png"
  alt="Part Level Badge"
/>

<br />

เมื่อเห็นสองอย่างนี้ แสดงว่าไฟล์นี้ใช้ @apollo/ui (v2)

<br />

### เข้าถึง v2 Documentation

Documentation จะอยู่ในสอง Section นี้ทางด้านซ้ายมือ

<img src="/v2-article/v2-section.png" style={{ maxWidth: "300px" }} />

หรือเข้าไปที่ [v2 Documentation](/docs/apollo∕ui-introduction--docs)

<br />

---

## ❓ คำถามที่พบบ่อย (FAQ)

<Accordion label="v1 จะเป็นยังไงต่อ" onOpenChange={() => {}}>
  <Typography level="bodyMedium">
    v1 จะยังใช้งานได้ปกติ เพียงแต่จะไม่มีการเพิ่มฟีเจอร์ใหม่ๆเข้ามาแล้ว เราจะ
    Support กรณีเกิดบัคต่างๆเท่านั้น
  </Typography>
</Accordion>

<br />

<Accordion label="ไม่ใช้ v2 ได้ไหม" onOpenChange={() => {}}>
  <Typography level="bodyMedium">
    ได้ครับ แต่ว่าเนื่องจากใน Figma ทาง UX/UI จะมีการใช้ v2 เป็นหลักในไฟล์ใหม่ๆ
    ทำให้ทุกครั้งที่เราต้องการทำ UI ท่ี่เห็น และการ Inspect
    ต่างๆต้องนำไปเทียบกับ v1 อยู่เสมอ รวมถึงบางอย่างใน v2 จะไม่มีใน v1 ทำให้ต้อง
    Customize เพิ่มเติมครับ
  </Typography>
</Accordion>

<br />

<Accordion label="อ่าว แบบนี้ อนาคตจะเปลี่ยนอีกไหมหล่ะ" onOpenChange={() => {}}>
  <div>
    <Typography level="bodyMedium">
      ก่อนอื่นเลยเราอยากขอโทษจากใจ
      ที่เกิดการเปลี่ยนแปลงขึ้นมาทำให้ต้องคอยปรับตัวตามเรา
    </Typography>

    <Typography level="headingSmall" style={{ margin: "16px 0" }}>
      คำตอบคือ "ไม่เปลี่ยนแล้วครับ" ขอสัญญา 🙏
    </Typography>

    <Typography level="bodyMedium">
      ในการเปลี่ยนแปลงครั้งนี้เราได้คิดมาแล้ว หากเราปล่อยให้ใช้ v1 ต่อไปเรื่อยๆ
      ปัญหาจะใหญ่กว่านี้เนื่องจาก Limitation ของ v1 ที่มีอยู่เยอะมาก
      ทั้งในการทำเพิ่ม หรือแก้ไขต่างๆ ทำได้ยากมากขึ้นเรื่อยๆ
    </Typography>

    <Typography level="bodyMedium" >
      ดังนั้นสรุปคือ @apollo/ui จะเป็น Library
      หลักที่เราจะพัฒนาและเพิ่มฟีเจอร์ใหม่ๆ ต่อไปในอนาคตยาวๆ ครับ
    </Typography>

  </div>
</Accordion>

<br />

<Accordion
  label="แล้วถ้าเป็นเคสที่อยากแก้ไขแค่ Component บางส่วนของหน้าล่ะ"
  onOpenChange={() => {}}
>
  <div>
    <Typography level="bodyMedium">
      เราเข้าใจว่าในบางครั้งเราอาจจะไม่สามารถเปลี่ยนจาก v1 เป็น v2
      ได้ทั้งหมดในครั้งเดียว
    </Typography>

    <Typography level="bodyMedium" >
      สามารถ import Component จาก @apollo/ui (v2) มาใช้ร่วมกับ Component จาก
      @design-systems/apollo-ui (v1) ได้เลย โดยเราได้มีวิธีทำให้ Component v2
      แสดงผลลัพธ์ออกมาให้เหมือนกับ v1 มากที่สุด สามารถทำตามนี้ได้เลย
    </Typography>
        [👉 คลิ๊กตรงนี้](/docs/apollo∕ui-switch-to-ui-styles-of-v1--docs)

  </div>
</Accordion>

<br />

<Accordion
  label="ถ้าเจอ UI ไม่ตรงกันอีก ทำยังไงได้บ้าง"
  onOpenChange={() => {}}
>
  <div>
    <Typography level="bodyMedium">
      ในเคสนี้เราอยากให้ทุกคนช่วยแจ้งบัคเข้ามาได้เลยครับ ช่วยกันทำให้ Design
      System ของเราดีขึ้นไปด้วยกัน
    </Typography>
    <Typography level="bodyMedium" >
      หากพบ UI ที่ไม่ตรงกับ Figma แสดงว่ามีอะไรบางอย่างผิดพลาดเกิดขึ้นแน่นอน
      สามารถติดต่อ UI-Engineer หรือ แจ้งกับ UX/UI ของโปรเจคได้เลย
    </Typography>

  </div>
</Accordion>

<br />
