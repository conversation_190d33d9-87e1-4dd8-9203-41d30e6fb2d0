import { useState } from "react"
import {
  createTheme,
  Radio,
  RadioGroup,
  ThemeProvider,
} from "@apollo/ui/legacy"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import MultiplePropsTable from "../../components/multiple-props-table/MultiplePropsTable"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Inputs/Radio",
  component: Radio,
  subcomponents: {
    RadioGroup,
  },
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", maxWidth: "400px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Radio and RadioGroup components allow users to select one option from a set. They support various layouts, states, and custom labels.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Radio, RadioGroup } from "@apollo/ui/legacy"`}
            language="tsx"
          />
          <h2 id="radio-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "Radio",
                props: [
                  {
                    name: "label",
                    description: "The label content displayed next to the radio button.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "value",
                    description: "The value associated with this radio button. Used by RadioGroup to track selection.",
                    type: "string | number",
                    defaultValue: "-",
                  },
                  {
                    name: "checked",
                    description: "Whether the radio button is checked (controlled mode).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "defaultChecked",
                    description: "Whether the radio button is checked by default (uncontrolled mode).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "disabled",
                    description: "Whether the radio button is disabled.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "name",
                    description: "The name attribute for the radio input. Used to group radio buttons.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "onChange",
                    description: "Callback fired when the radio button is selected.",
                    type: "(event: React.ChangeEvent<HTMLInputElement>) => void",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "RadioGroup",
                props: [
                  {
                    name: "value",
                    description: "The currently selected value (controlled mode).",
                    type: "string | number",
                    defaultValue: "-",
                  },
                  {
                    name: "defaultValue",
                    description: "The default selected value (uncontrolled mode).",
                    type: "string | number",
                    defaultValue: "-",
                  },
                  {
                    name: "onChange",
                    description: "Callback fired when the selected value changes.",
                    type: "(value: string | number, event?: React.ChangeEvent) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "direction",
                    description: "The layout direction of the radio buttons.",
                    defaultValue: '"vertical"',
                    type: '"horizontal" | "vertical"',
                  },
                  {
                    name: "name",
                    description: "The name attribute applied to all radio buttons in the group.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "children",
                    description: "Radio components to be rendered within the group.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="radio-examples" style={{ margin: '20px 0px' }}>Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Radio>

export default meta
type Story = StoryObj<typeof meta>

// Single radio button (not recommended for real use)
export const SingleRadio: Story = {
  args: {
    label: "Single option",
    value: "single",
  },
  parameters: {
    docs: {
      description: {
        story:
          "A single radio button. Note: Radio buttons should typically be used in groups.",
      },
    },
  },
}

// Basic RadioGroup
export const BasicGroup: Story = {
  render: () => (
    <RadioGroup value="apple" onChange={() => {}}>
      <Radio value="apple" label="Apple" />
      <Radio value="banana" label="Banana" />
      <Radio value="cherry" label="Cherry" />
    </RadioGroup>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Basic radio group with three options. One option is pre-selected.",
      },
    },
  },
}

// Horizontal layout
export const HorizontalGroup: Story = {
  render: () => (
    <RadioGroup direction="horizontal" value="medium" onChange={() => {}}>
      <Radio value="small" label="Small" />
      <Radio value="medium" label="Medium" />
      <Radio value="large" label="Large" />
    </RadioGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: "Radio group with horizontal layout.",
      },
    },
  },
}

// Disabled states
export const DisabledGroup: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Entire group disabled:</h4>
        <RadioGroup disabled value="option1" onChange={() => {}}>
          <Radio value="option1" label="Option 1" />
          <Radio value="option2" label="Option 2" />
          <Radio value="option3" label="Option 3" />
        </RadioGroup>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Individual radios disabled:</h4>
        <RadioGroup value="option1" onChange={() => {}}>
          <Radio value="option1" label="Available option" />
          <Radio value="option2" label="Disabled option" disabled />
          <Radio value="option3" label="Another available option" />
        </RadioGroup>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Examples of disabled radio buttons and groups.",
      },
    },
  },
}

// Controlled example
export const ControlledGroup: Story = {
  render: () => {
    const [value, setValue] = useState("option1")

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <RadioGroup value={value} onChange={(newValue) => setValue(newValue)}>
          <Radio value="option1" label="Option 1" />
          <Radio value="option2" label="Option 2" />
          <Radio value="option3" label="Option 3" />
        </RadioGroup>

        <div
          style={{ padding: "8px", background: "#f5f5f5", borderRadius: "4px" }}
        >
          <strong>Selected value:</strong> {value}
        </div>

        <div style={{ display: "flex", gap: "8px" }}>
          <button
            onClick={() => setValue("option1")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Select Option 1
          </button>
          <button
            onClick={() => setValue("option2")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Select Option 2
          </button>
          <button
            onClick={() => setValue("option3")}
            style={{ padding: "4px 8px", fontSize: "12px" }}
          >
            Select Option 3
          </button>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Controlled radio group with external state management and programmatic selection.",
      },
    },
  },
}

// Form usage example
export const FormUsage: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      plan: "basic",
      billing: "monthly",
      support: "email",
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      alert(`Form submitted with: ${JSON.stringify(formData, null, 2)}`)
    }

    return (
      <form
        onSubmit={handleSubmit}
        style={{ display: "flex", flexDirection: "column", gap: "24px" }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>Choose Your Plan</h3>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            Subscription Plan:
          </label>
          <RadioGroup
            value={formData.plan}
            onChange={(value) =>
              setFormData((prev) => ({ ...prev, plan: value }))
            }
          >
            <Radio value="basic" label="Basic - $9/month" />
            <Radio value="pro" label="Pro - $19/month" />
            <Radio value="enterprise" label="Enterprise - $49/month" />
          </RadioGroup>
        </div>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            Billing Frequency:
          </label>
          <RadioGroup
            value={formData.billing}
            onChange={(value) =>
              setFormData((prev) => ({ ...prev, billing: value }))
            }
            direction="horizontal"
          >
            <Radio value="monthly" label="Monthly" />
            <Radio value="yearly" label="Yearly (Save 20%)" />
          </RadioGroup>
        </div>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            Support Level:
          </label>
          <RadioGroup
            value={formData.support}
            onChange={(value) =>
              setFormData((prev) => ({ ...prev, support: value }))
            }
          >
            <Radio value="email" label="Email support" />
            <Radio value="chat" label="Chat support" />
            <Radio value="phone" label="Phone support" />
          </RadioGroup>
        </div>

        <button
          type="submit"
          style={{
            padding: "12px 24px",
            background: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
            fontSize: "16px",
          }}
        >
          Subscribe Now
        </button>
      </form>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Example showing radio groups in a real form with multiple sections and controlled state.",
      },
    },
  },
}

// Custom styling example
export const CustomLabels: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Priority Levels:</h4>
        <RadioGroup value="medium" onChange={() => {}}>
          <Radio value="low" label="🟢 Low Priority" />
          <Radio value="medium" label="🟡 Medium Priority" />
          <Radio value="high" label="🔴 High Priority" />
          <Radio value="urgent" label="⚡ Urgent" />
        </RadioGroup>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Payment Methods:</h4>
        <RadioGroup value="card" onChange={() => {}}>
          <Radio value="card" label="💳 Credit Card" />
          <Radio value="paypal" label="📱 PayPal" />
          <Radio value="bank" label="🏦 Bank Transfer" />
          <Radio value="crypto" label="₿ Cryptocurrency" />
        </RadioGroup>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Examples with custom labels including emojis and icons.",
      },
    },
  },
}

// All states comparison
export const AllStates: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(2, 1fr)",
        gap: "20px",
      }}
    >
      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Normal States:</h4>
        <RadioGroup value="selected" onChange={() => {}}>
          <Radio value="unselected" label="Unselected" />
          <Radio value="selected" label="Selected" />
        </RadioGroup>
      </div>

      <div>
        <h4 style={{ margin: "0 0 8px 0" }}>Disabled States:</h4>
        <RadioGroup value="disabled-selected" onChange={() => {}}>
          <Radio
            value="disabled-unselected"
            label="Disabled Unselected"
            disabled
          />
          <Radio value="disabled-selected" label="Disabled Selected" disabled />
        </RadioGroup>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Comparison of all possible radio button states.",
      },
    },
  },
}

// Survey example
export const SurveyExample: Story = {
  render: () => {
    const [answers, setAnswers] = useState({
      satisfaction: "",
      frequency: "",
      recommend: "",
    })

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          maxWidth: "500px",
        }}
      >
        <h3 style={{ margin: 0, fontSize: "18px" }}>
          Customer Satisfaction Survey
        </h3>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            How satisfied are you with our service?
          </label>
          <RadioGroup
            value={answers.satisfaction}
            onChange={(value) =>
              setAnswers((prev) => ({ ...prev, satisfaction: value }))
            }
          >
            <Radio value="very-satisfied" label="Very Satisfied" />
            <Radio value="satisfied" label="Satisfied" />
            <Radio value="neutral" label="Neutral" />
            <Radio value="dissatisfied" label="Dissatisfied" />
            <Radio value="very-dissatisfied" label="Very Dissatisfied" />
          </RadioGroup>
        </div>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            How often do you use our service?
          </label>
          <RadioGroup
            value={answers.frequency}
            onChange={(value) =>
              setAnswers((prev) => ({ ...prev, frequency: value }))
            }
          >
            <Radio value="daily" label="Daily" />
            <Radio value="weekly" label="Weekly" />
            <Radio value="monthly" label="Monthly" />
            <Radio value="rarely" label="Rarely" />
          </RadioGroup>
        </div>

        <div>
          <label
            style={{
              fontWeight: "bold",
              marginBottom: "8px",
              display: "block",
            }}
          >
            Would you recommend us to others?
          </label>
          <RadioGroup
            value={answers.recommend}
            onChange={(value) =>
              setAnswers((prev) => ({ ...prev, recommend: value }))
            }
            direction="horizontal"
          >
            <Radio value="yes" label="Yes" />
            <Radio value="no" label="No" />
            <Radio value="maybe" label="Maybe" />
          </RadioGroup>
        </div>

        {Object.values(answers).every(Boolean) && (
          <div
            style={{
              padding: "12px",
              background: "#e8f5e8",
              border: "1px solid #4caf50",
              borderRadius: "4px",
              fontSize: "14px",
            }}
          >
            ✅ Survey completed! Thank you for your feedback.
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive survey example showing multiple radio groups working together.",
      },
    },
  },
}
