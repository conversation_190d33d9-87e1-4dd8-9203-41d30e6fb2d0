import { useState } from "react"
import {
  Sidebar,
  createTheme,
  ThemeProvider,
  Typography,
  MenuItem,
  MenuItemGroup,
} from "@apollo/ui/legacy"
import {
  Home,
  Shop,
  Shopping,
  Solution,
  User,
  Setting,
} from "@design-systems/apollo-icons"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import MultiplePropsTable from "../../components/multiple-props-table/MultiplePropsTable"

// Need for legacy
import "../../app/tailwind.css"

const meta = {
  title: "@design-systems∕apollo-ui/Components/Navigation/Sidebar",
  component: Sidebar,
  subcomponents: {
    MenuItem,
    MenuItemGroup,
  },
  decorators: [
    (Story) => (
      <ThemeProvider theme={createTheme()}>
        <div style={{ padding: "20px", height: "600px" }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Sidebar provides a vertical navigation component with collapsible menu items, headers, and footer sections. It supports nested menus, selection states, and responsive behavior.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Sidebar, MenuItem, MenuItemGroup } from "@apollo/ui/legacy"`}
            language="tsx"
          />
          <h2 id="sidebar-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "Sidebar",
                props: [
                  {
                    name: "title",
                    description: "Title displayed in the sidebar header.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "logo",
                    description: "Logo element to display in the header.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "header",
                    description: "Custom header content to replace the default header.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "menus",
                    description: "Array of menu items and groups to display in the sidebar.",
                    type: "SidebarMenu[]",
                    defaultValue: "-",
                  },
                  {
                    name: "footer",
                    description: "Footer content (can be SidebarMenu[] or custom ReactNode).",
                    type: "SidebarMenu[] | ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "selectedMenuKey",
                    description: "Key of the currently selected menu item.",
                    type: "string | number",
                    defaultValue: "-",
                  },
                  {
                    name: "onSelectMenu",
                    description: "Callback fired when a menu item is selected.",
                    type: "(key: string | number, groupKey?: string | number | null) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "expandedMenuKeys",
                    description: "Array of keys for expanded menu groups.",
                    type: "Array<string | number>",
                    defaultValue: "-",
                  },
                  {
                    name: "onExpandedChange",
                    description: "Callback fired when a menu group is expanded or collapsed.",
                    type: "(key: string | number, expanded: boolean, groupKey?: string) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "collapsible",
                    description: "Whether the sidebar can be collapsed.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "collapsed",
                    description: "Whether the sidebar is currently collapsed (controlled mode).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "onCollapsedChange",
                    description: "Callback fired when the collapsed state changes.",
                    type: "(collapsed: boolean) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "width",
                    description: "Width of the sidebar when expanded.",
                    defaultValue: '"240px"',
                    type: "string",
                  },
                  {
                    name: "onLogOut",
                    description: "Callback fired when the logout button is clicked.",
                    type: "() => void",
                    defaultValue: "-",
                  },
                  {
                    name: "logOutButtonLabel",
                    description: "Label for the logout button.",
                    defaultValue: '"Log out"',
                    type: "string",
                  },
                ],
              },
              {
                label: "MenuItem",
                props: [
                  {
                    name: "label",
                    description: "The text label for the menu item.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "icon",
                    description: "Icon element to display before the label.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "selected",
                    description: "Whether the menu item is currently selected.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "disabled",
                    description: "Whether the menu item is disabled.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "subItem",
                    description: "Whether this is a sub-item (indented).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "onlyIcon",
                    description: "Whether to show only the icon (for collapsed sidebar).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "href",
                    description: "URL to navigate to when the item is clicked.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "LinkComponent",
                    description: "Custom link component to use for navigation.",
                    defaultValue: '"a"',
                    type: "React.ComponentType",
                  },
                  {
                    name: "endDecorator",
                    description: "Element to display at the end of the menu item.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "onClick",
                    description: "Callback fired when the menu item is clicked.",
                    type: "() => void",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "MenuItemGroup",
                props: [
                  {
                    name: "label",
                    description: "The text label for the menu group.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "icon",
                    description: "Icon element to display before the label.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "selected",
                    description: "Whether the menu group is currently selected.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "expanded",
                    description: "Whether the menu group is expanded (showing children).",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "onExpandedStateChange",
                    description: "Callback fired when the expanded state changes.",
                    type: "(expanded: boolean) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "collapseIcon",
                    description: "Custom icon to use for the collapse/expand indicator.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "children",
                    description: "MenuItem components to display when the group is expanded.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="sidebar-examples" style={{ margin: '20px 0px' }}>Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Sidebar>

export default meta
type Story = StoryObj<typeof meta>

const basicMenus = [
  {
    key: "home",
    icon: <Home />,
    label: "Home",
  },
  {
    key: "shop",
    icon: <Shop />,
    label: "Shop",
  },
  {
    key: "orders",
    icon: <Shopping />,
    label: "Orders",
  },
  {
    key: "profile",
    icon: <User />,
    label: "Profile",
  },
]

const complexMenus = [
  {
    key: "dashboard",
    icon: <Home />,
    label: "Dashboard",
  },
  {
    key: "ecommerce",
    icon: <Shop />,
    label: "E-commerce",
    children: [
      { key: "products", label: "Products" },
      { key: "orders", label: "Orders" },
      { key: "customers", label: "Customers" },
    ],
  },
  {
    key: "analytics",
    icon: <Solution />,
    label: "Analytics",
    children: [
      { key: "reports", label: "Reports" },
      { key: "insights", label: "Insights" },
    ],
  },
  {
    key: "settings",
    icon: <Setting />,
    label: "Settings",
    children: [
      { key: "general", label: "General" },
      { key: "security", label: "Security" },
      { key: "notifications", label: "Notifications" },
    ],
  },
]

// Basic Sidebar
export const Basic: Story = {
  args: {
    menus: basicMenus,
  },
  render: function BasicComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ height: "500px", border: "1px solid #e9ecef", borderRadius: "8px", background: "#f8f9fa" }}>
        <Sidebar
          title="Apollo"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Basic sidebar with simple menu items and selection state.",
      },
    },
  },
}

// Collapsible Sidebar
export const Collapsible: Story = {
  args: {
    menus: basicMenus,
  },
  render: function CollapsibleComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")
    const [collapsed, setCollapsed] = useState(false)

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px", transition: "width 0.3s ease" }}>
        <Sidebar
          title="Apollo"
          collapsible
          collapsed={collapsed}
          onCollapsedChange={setCollapsed}
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Collapsible sidebar that can be toggled between expanded and collapsed states.",
      },
    },
  },
}

// With nested menus
export const WithNestedMenus: Story = {
  args: {
    menus: complexMenus,
  },
  render: function WithNestedMenusComponent() {
    const [selectedMenu, setSelectedMenu] = useState("dashboard")
    const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["ecommerce"])

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Admin Panel"
          menus={complexMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
          expandedMenuKeys={expandedMenuKeys}
          onExpandedChange={(key, expanded) => {
            if (expanded) {
              setExpandedMenuKeys([...expandedMenuKeys, String(key)])
            } else {
              setExpandedMenuKeys(expandedMenuKeys.filter(k => k !== String(key)))
            }
          }}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with nested menu groups that can be expanded and collapsed.",
      },
    },
  },
}

// With footer
export const WithFooter: Story = {
  args: {
    menus: basicMenus,
  },
  render: function WithFooterComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Apollo"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
          footer={
            <MenuItemGroup icon={<User />} label="John Doe" selected>
              <MenuItem label="Profile" subItem />
              <MenuItem label="Settings" subItem />
            </MenuItemGroup>
          }
          onLogOut={() => alert("Logged out!")}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with footer section containing user profile and logout functionality.",
      },
    },
  },
}

// Custom width
export const CustomWidth: Story = {
  args: {
    menus: basicMenus,
  },
  render: function CustomWidthComponent() {
    const [selectedMenu, setSelectedMenu] = useState("home")

    return (
      <div style={{ background: "#f8f9fa", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
        <Sidebar
          title="Wide Sidebar"
          width="400px"
          menus={basicMenus}
          selectedMenuKey={selectedMenu}
          onSelectMenu={(key) => setSelectedMenu(String(key))}
        />
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Sidebar with custom width for different layout requirements.",
      },
    },
  },
}

// Full featured
export const FullFeatured: Story = {
  args: {
    menus: complexMenus,
  },
  render: function FullFeaturedComponent() {
    const [selectedMenu, setSelectedMenu] = useState("products")
    const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["ecommerce"])
    const [collapsed, setCollapsed] = useState(false)

    return (
      <div style={{ display: "flex", height: "500px", border: "1px solid #e9ecef", borderRadius: "8px" }}>
          <Sidebar
            title="Admin Dashboard"
            collapsible
            collapsed={collapsed}
            onCollapsedChange={setCollapsed}
            menus={complexMenus}
            selectedMenuKey={selectedMenu}
            onSelectMenu={(key) => setSelectedMenu(String(key))}
            expandedMenuKeys={expandedMenuKeys}
            onExpandedChange={(key, expanded) => {
              if (expanded) {
                setExpandedMenuKeys([...expandedMenuKeys, String(key)])
              } else {
                setExpandedMenuKeys(expandedMenuKeys.filter(k => k !== String(key)))
              }
            }}
            footer={
              <MenuItemGroup icon={<User />} label="Admin User" selected>
                <MenuItem label="My Profile" subItem />
                <MenuItem label="Account Settings" subItem />
              </MenuItemGroup>
            }
            onLogOut={() => alert("Admin logged out!")}
            logOutButtonLabel="Sign Out"
          />
        <div style={{ flex: 1, padding: "24px", backgroundColor: "#f8f9fa" }}>
          <Typography level="h3" style={{ marginBottom: "16px" }}>
            Main Content Area
          </Typography>
          <Typography level="body-1" style={{ color: "#6c757d" }}>
            Selected menu: <strong>{selectedMenu}</strong>
          </Typography>
          <Typography level="body-1" style={{ color: "#6c757d", marginTop: "8px" }}>
            Sidebar is {collapsed ? "collapsed" : "expanded"}
          </Typography>
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: "Full-featured sidebar with all options including collapsible state, nested menus, footer, and main content integration.",
      },
    },
  },
}
