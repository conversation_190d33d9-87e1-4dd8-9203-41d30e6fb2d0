# llms.txt Generation Script

This directory contains the automated script for generating [llmstxt.org](https://llmstxt.org/)-compliant documentation for the Apollo Design System.

## Overview

The `generate-llms-txt.ts` script:

- ✅ Analyzes all 26 @apollo/ui Storybook stories
- ✅ Extracts component metadata, props, and examples
- ✅ Generates `/llms.txt` index file
- ✅ Creates individual component markdown files
- ✅ Organizes by category (Inputs, Layout, Navigation, etc.)

## Generated Structure

```
public/
├── llms.txt                              # Main index file
└── llms/
    └── components/
        ├── inputs/
        │   ├── button.md
        │   ├── input.md
        │   ├── checkbox.md
        │   └── ... (12 components)
        ├── layout/
        │   ├── accordion.md
        │   ├── tabs.md
        │   └── capsuletab.md
        ├── navigation/
        │   ├── breadcrumbs.md
        │   └── pagination.md
        ├── data-display/
        │   ├── typography.md
        │   ├── badge.md
        │   ├── chip.md
        │   └── sortingicon.md
        ├── feedback/
        │   ├── alert.md
        │   ├── modal.md
        │   └── toast.md
        ├── utilities/
        │   └── portal.md
        └── theming/
            └── theme.md
```

## Usage

### Generate Documentation

```bash
# From apollo-docs directory
pnpm generate:llms-txt
```

### Output

The script will:

1. Parse all `.stories.tsx` files in `src/stories/apollo-ui/`
2. Extract for each component:
   - Component name and category
   - Description from JSDoc or meta
   - Props from argTypes (name, type, default, description)
   - Code examples from story variants
   - Usage guidelines
   - Accessibility notes
   - Figma design links
3. Generate `public/llms.txt` (main index)
4. Generate `public/llms/components/**/*.md` (26 markdown files)

### View Generated Files

```bash
# View main index
cat public/llms.txt

# View specific component
cat public/llms/components/inputs/button.md

# List all generated files
find public/llms -name "*.md"
```

## Generated Content Example

### /llms.txt (Main Index)

```markdown
# Apollo Design System

> Apollo is a comprehensive React design system providing 26+ production-ready components...

## Components

### Inputs

- [Button](/llms/components/inputs/button.md): Button with multiple variants...
- [Input](/llms/components/inputs/input.md): Text input with validation...
  ...
```

### /llms/components/inputs/button.md

```markdown
# Button

> Button component with multiple variants, sizes, and colors

## Installation

\`\`\`tsx
import { Button } from "@apollo/ui"
\`\`\`

## Props

| Prop    | Type                            | Default | Description  |
| ------- | ------------------------------- | ------- | ------------ |
| variant | "filled" \| "outline" \| "text" | filled  | Visual style |
| size    | "large" \| "small"              | large   | Button size  |

...

## Examples

### With Icon

\`\`\`tsx
<Button startDecorator={<Icon />}>
Download
</Button>
\`\`\`

## Accessibility

- Uses semantic button element
- Supports keyboard navigation
  ...
```

## What Gets Extracted

### ✅ Currently Working

- **Component metadata**: Name, category, description
- **Props**: Name, type, default value, description
- **Examples**: Basic usage from stories
- **Guidelines**: Usage best practices
- **Accessibility**: A11y notes
- **Design links**: Figma URLs

### 🚧 Could Be Improved

- **Prop types**: Some showing as "unknown" (need better regex)
- **Examples**: Could extract actual JSX from story render functions
- **Guidelines**: Plain text extraction (could preserve formatting)
- **Code examples**: Currently simplified (could be more accurate)

## How It Works

### 1. Component Metadata Extraction

```typescript
// Extracts from story file:
const meta = {
  title: "@apollo∕ui/Components/Inputs/Button", // → category: "Inputs"
  component: Button, // → name: "Button"
  parameters: {
    design: {
      url: "https://figma.com/...", // → figmaUrl
    },
    docs: {
      description: {
        component: "Description...", // → description
      },
    },
  },
}
```

### 2. Props Extraction

```typescript
// Extracts from argTypes:
argTypes: {
  variant: {
    description: "Visual style variant",          // → description
    table: {
      type: { summary: '"filled" | "outline"' }, // → type
      defaultValue: { summary: "filled" }         // → defaultValue
    }
  }
}
```

### 3. Examples Extraction

```typescript
// Extracts from story exports:
export const WithIcon: Story = {
  args: {
    children: "Download",
    startDecorator: <Icon />
  }
}
// → Generates code example
```

## Integration with Build Pipeline

### Option 1: Manual Generation

```bash
# Run before build
pnpm generate:llms-txt
pnpm build-storybook
```

### Option 2: Pre-build Hook

```json
// package.json
{
  "scripts": {
    "prebuild-storybook": "pnpm generate:llms-txt",
    "build-storybook": "storybook build"
  }
}
```

### Option 3: Watch Mode (Development)

```json
{
  "scripts": {
    "dev:with-llms": "concurrently \"pnpm dev\" \"pnpm watch:llms\"",
    "watch:llms": "nodemon --watch src/stories --ext tsx --exec 'pnpm generate:llms-txt'"
  }
}
```

## Testing with LLMs

### 1. Local Testing

```bash
# Start Storybook dev server
pnpm dev

# Access llms.txt
open http://localhost:6006/llms.txt
```

### 2. Test with Claude/ChatGPT

```
Prompt: "Read the Apollo Design System documentation at /llms.txt and help me use the Button component"
```

### 3. Test with Cursor/Copilot

Add to your `.cursorrules` or workspace settings:

```
The project uses Apollo Design System (@apollo/ui).
Component documentation is available at /llms.txt
```

## Maintenance

### When to Regenerate

- ✅ After adding new components
- ✅ After updating component props
- ✅ After changing story examples
- ✅ Before publishing new versions
- ✅ When deploying to production

### Continuous Integration

```yaml
# .gitlab-ci.yml or .github/workflows/build.yml
- name: Generate LLM Documentation
  run: |
    cd apps/apollo-docs
    pnpm generate:llms-txt

- name: Build Storybook
  run: |
    cd apps/apollo-docs
    pnpm build-storybook
```

## Future Improvements

### High Priority

- [ ] Better type extraction (handle union types, enums)
- [ ] Extract actual JSX from story render functions
- [ ] Add component relationships (uses, extends)
- [ ] Include design tokens documentation

### Medium Priority

- [ ] Add search index for components
- [ ] Generate `.md` versions of story pages (per llmstxt.org spec)
- [ ] Add changelog/version info
- [ ] Include common patterns/recipes

### Low Priority

- [ ] Generate diagrams (component tree, props flow)
- [ ] Add usage statistics
- [ ] Include TypeScript definitions
- [ ] Add interactive examples

## Troubleshooting

### Props not extracted

**Issue**: Props showing as "unknown" type

**Solution**: Check argTypes structure in story file. Ensure:

```typescript
argTypes: {
  propName: {
    description: "...",
    table: {
      type: { summary: "string" }
    }
  }
}
```

### Examples not working

**Issue**: Code examples are empty or incorrect

**Solution**: Check story exports have `args`:

```typescript
export const Example: Story = {
  args: {
    children: "Text",
    variant: "filled",
  },
}
```

### Component not found

**Issue**: Component not in generated files

**Solution**: Ensure story file:

- Is in `src/stories/apollo-ui/`
- Has `.stories.tsx` extension
- Exports a `meta` object with `title`

## Resources

- [llmstxt.org Specification](https://llmstxt.org/)
- [Apollo Design System](https://cjx-apollo-ui.netlify.app/)
- [Storybook Documentation](https://storybook.js.org/)

## License

Internal tool for Apollo Design System.

---

**Last Updated**: October 16, 2025  
**Script Version**: 1.0.0  
**Components Documented**: 26
