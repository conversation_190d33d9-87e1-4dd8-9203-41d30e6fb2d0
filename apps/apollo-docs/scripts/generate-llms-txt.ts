#!/usr/bin/env tsx
import fs from "fs/promises"
import path from "path"
import { fileURLToPath } from "url"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

interface Component {
  name: string
  category: string
  categorySlug: string
  description: string
  storyPath: string
  figmaUrl?: string
  props: PropInfo[]
  examples: Example[]
  usageGuidelines: string[]
  accessibilityNotes: string[]
}

interface PropInfo {
  name: string
  type: string
  defaultValue: string
  description: string
  source?: "argTypes" | "propsFile" | "merged"
}

interface Example {
  title: string
  code: string
  description: string
}

/**
 * Extract component metadata from story files
 */
async function extractComponentMetadata(
  storyFile: string
): Promise<Component | null> {
  const storiesDir = path.join(__dirname, "../src/stories/apollo-ui")
  const filePath = path.join(storiesDir, storyFile)
  const content = await fs.readFile(filePath, "utf-8")

  // Extract title from meta object
  const titleMatch = content.match(/title:\s*["']([^"']+)["']/)
  if (!titleMatch) return null

  const fullTitle = titleMatch[1]
  const parts = fullTitle.split("/")
  const category = parts[parts.length - 2] // e.g., "Inputs", "Layout"
  const name = parts[parts.length - 1]

  // Extract description from docs.description.component or JSDoc comment
  let description = ""
  const componentDescMatch = content.match(/component:\s*["']([^"']+)["']/)
  if (componentDescMatch) {
    description = componentDescMatch[1]
  } else {
    // Try JSDoc comment
    const jsDocMatch = content.match(/\/\*\*[\s\S]*?\*\s*(.+?)\s*\n/)
    if (jsDocMatch) {
      description = jsDocMatch[1]
    }
  }

  // Extract Figma URL
  const figmaMatch = content.match(/url:\s*["']([^"']+figma[^"']+)["']/)
  const figmaUrl = figmaMatch?.[1]

  // Extract props from argTypes
  const propsFromArgTypes = extractProps(content)

  // Try to get props from TypeScript props file
  const propsFromFile = await extractPropsFromFile(name)

  // Merge props (prioritize argTypes descriptions, but use file types)
  const props = mergeProps(propsFromArgTypes, propsFromFile)

  // Extract examples from stories
  const examples = extractExamples(content, name)

  // Extract usage guidelines
  const usageGuidelines = extractGuidelines(content, "Best Practices")
  if (usageGuidelines.length === 0) {
    // Try alternative section name
    const altGuidelines = extractGuidelines(content, "Usage")
    usageGuidelines.push(...altGuidelines)
  }

  // Extract accessibility notes
  const accessibilityNotes = extractGuidelines(content, "Accessibility")

  const categorySlug = category.toLowerCase().replace(/\s+/g, "-")

  return {
    name,
    category,
    categorySlug,
    description,
    storyPath: storyFile,
    figmaUrl,
    props,
    examples,
    usageGuidelines,
    accessibilityNotes,
  }
}

/**
 * Extract props information from argTypes
 */
function extractProps(content: string): PropInfo[] {
  const props: PropInfo[] = []

  // Find argTypes section - look for it between argTypes: { and the closing }
  const argTypesStart = content.indexOf("argTypes:")
  if (argTypesStart === -1) return props

  // Find the opening brace
  const openBrace = content.indexOf("{", argTypesStart)
  if (openBrace === -1) return props

  // Find the matching closing brace by counting braces
  let braceCount = 1
  let closeBrace = openBrace + 1
  while (closeBrace < content.length && braceCount > 0) {
    if (content[closeBrace] === "{") braceCount++
    if (content[closeBrace] === "}") braceCount--
    closeBrace++
  }

  const argTypesContent = content.substring(openBrace + 1, closeBrace - 1)

  // Parse using a more sophisticated approach that handles both single-line and multi-line props
  // Split into lines and track brace depth
  const lines = argTypesContent.split("\n")
  let currentProp: string | null = null
  let currentContent: string[] = []
  let braceDepth = 0

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    // Check if this line starts a new top-level prop definition at depth 0
    // Match: "  propName: {" or "  propName: { ... }" (single or multi-line)
    const propMatch = line.match(/^\s{2,8}(\w+):\s*\{/)

    if (propMatch && braceDepth === 0) {
      // Process previous prop if exists
      if (currentProp && currentContent.length > 0) {
        const prop = parsePropContent(currentProp, currentContent.join("\n"))
        if (prop) props.push(prop)
      }

      // Start new prop
      currentProp = propMatch[1]
      currentContent = [line]

      // Count braces in this line to determine if it's single-line or multi-line
      const openBraces = (line.match(/\{/g) || []).length
      const closeBraces = (line.match(/\}/g) || []).length
      braceDepth = openBraces - closeBraces
    } else if (currentProp !== null) {
      // We're inside a prop definition
      currentContent.push(line)

      // Update brace depth
      const openBraces = (line.match(/\{/g) || []).length
      const closeBraces = (line.match(/\}/g) || []).length
      braceDepth += openBraces - closeBraces
    }
  }

  // Process last prop
  if (currentProp && currentContent.length > 0) {
    const prop = parsePropContent(currentProp, currentContent.join("\n"))
    if (prop) props.push(prop)
  }

  return props
}

/**
 * Parse individual prop content
 */
function parsePropContent(
  propName: string,
  propContent: string
): PropInfo | null {
  // Extract description
  const descMatch = propContent.match(/description:\s*["'`]([^"'`]+)["'`]/)
  const description = descMatch?.[1] || ""

  // Skip control-only props without description
  if (
    !description &&
    propContent.includes("control:") &&
    !propContent.includes("table:")
  ) {
    return null
  }

  // Extract type from table.type.summary (handles both single-line and multi-line)
  // Use more flexible regex that can handle nested quotes
  let type = "unknown"

  // Try to match: table: { type: { summary: "..." } } or table: { type: { summary: '...' } }
  // This regex captures content between quotes, including quotes inside
  const tableSummaryMatch = propContent.match(
    /table:\s*\{[\s\S]*?type:\s*\{\s*summary:\s*["'](.+?)["']\s*[,}]/
  )
  if (tableSummaryMatch) {
    type = tableSummaryMatch[1]
  }

  // Extract default value from table.defaultValue.summary
  let defaultValue = "-"
  const defaultMatch = propContent.match(
    /(?:table:\s*\{[\s\S]*?)?defaultValue:\s*\{\s*summary:\s*["'](.+?)["']\s*[,}]/
  )
  if (defaultMatch) {
    defaultValue = defaultMatch[1]
  }

  return {
    name: propName,
    type,
    defaultValue,
    description: description || "No description available",
    source: "argTypes" as const,
  }
}

/**
 * Find and extract props from TypeScript props file
 */
async function extractPropsFromFile(
  componentName: string
): Promise<PropInfo[]> {
  const props: PropInfo[] = []

  try {
    // Try to find the props file
    const apolloUiPath = path.join(
      __dirname,
      "../../../packages/apollo-ui/src/components"
    )
    const componentFolder = componentName
      .toLowerCase()
      .replace(/([a-z])([A-Z])/g, "$1-$2")
      .toLowerCase()

    // Try different possible filenames
    const possibleFiles = [
      path.join(apolloUiPath, componentFolder, `${componentName}Props.ts`),
      path.join(
        apolloUiPath,
        componentFolder,
        `${componentName.toLowerCase()}Props.ts`
      ),
      path.join(apolloUiPath, componentFolder, "types.ts"),
      path.join(apolloUiPath, componentFolder, `${componentName}.types.ts`),
    ]

    let propsFileContent: string | null = null
    let foundFile: string | null = null

    for (const filePath of possibleFiles) {
      try {
        propsFileContent = await fs.readFile(filePath, "utf-8")
        foundFile = filePath
        break
      } catch {
        // File doesn't exist, try next
        continue
      }
    }

    if (!propsFileContent) return props

    // Look for multiple possible Props types (ComponentProps, ComponentGroupProps, BaseComponentProps, etc.)
    const possibleTypeNames = [
      `${componentName}Props`,
      `Base${componentName}Props`,
      `${componentName}GroupProps`,
      `${componentName.toLowerCase()}Props`,
    ]

    for (const typeName of possibleTypeNames) {
      // Find the Props type/interface - handle multiline and intersection types
      const typeRegex = new RegExp(
        `export (type|interface) ${typeName}[<\\w\\s=,>]*[=\\s]+\\{([\\s\\S]+?)\\n\\}(?:\\s*&|;|$)`
      )
      const match = propsFileContent.match(typeRegex)

      if (match) {
        const propsContent = match[2]

        // Extract individual properties
        const propLines = propsContent.split("\n")
        for (const line of propLines) {
          // Match: propName?: Type or propName: Type
          // Also capture JSDoc comments above the prop
          const propMatch = line.match(/^\s*(\w+)\??:\s*(.+?)(?:\s*\/\/.*)?$/)
          if (propMatch) {
            const [, propName, propType] = propMatch

            // Clean up the type (remove trailing commas, etc.)
            let cleanType = propType.trim().replace(/,\s*$/, "")

            // Simplify complex types for readability
            if (cleanType.includes("=>")) {
              // Function type - keep signature
              cleanType = cleanType
            } else if (cleanType.startsWith('"') && cleanType.includes("|")) {
              // Union of string literals - keep it
              cleanType = cleanType
            }

            props.push({
              name: propName,
              type: cleanType,
              defaultValue: "-",
              description: "",
              source: "propsFile" as const,
            })
          }
        }
      }
    }

    if (props.length === 0) {
      console.log(
        `   ℹ Could not extract props from file for ${componentName}`
      )
    }
  } catch (error) {
    // Silently fail if we can't read the props file
    console.log(
      `   ℹ Error extracting props from file for ${componentName}:`,
      error
    )
  }

  return props
}

/**
 * Merge props from argTypes and props file
 * Priority: argTypes descriptions + props file types
 */
function mergeProps(
  argTypesProps: PropInfo[],
  fileProps: PropInfo[]
): PropInfo[] {
  const merged: PropInfo[] = []
  const propMap = new Map<string, PropInfo>()

  // Start with argTypes (has descriptions)
  for (const prop of argTypesProps) {
    propMap.set(prop.name, { ...prop })
  }

  // Enhance with types from props file
  for (const fileProp of fileProps) {
    const existing = propMap.get(fileProp.name)
    if (existing) {
      // Merge: keep argTypes description, use file type if better
      if (existing.type === "unknown" || existing.type === "any") {
        existing.type = fileProp.type
        existing.source = "merged" as const
      }
    } else {
      // Add props that only exist in file
      propMap.set(fileProp.name, fileProp)
    }
  }

  // Convert map back to array and sort by name
  return Array.from(propMap.values()).sort((a, b) =>
    a.name.localeCompare(b.name)
  )
}

/**
 * Extract code examples from story exports
 */
function extractExamples(content: string, componentName: string): Example[] {
  const examples: Example[] = []

  // Find story exports - match: export const StoryName: Story = { ... }
  const storyRegex = /export const (\w+):\s*Story\s*=\s*\{([\s\S]+?)\n\}/g
  let match: RegExpExecArray | null

  let count = 0
  while ((match = storyRegex.exec(content)) !== null && count < 5) {
    const storyName = match[1]
    const storyContent = match[2]

    // Skip Overview/Default stories - they're usually too basic
    if (storyName === "Overview" || storyName === "Default") continue

    // Extract description from JSDoc comment above the story or from story property
    let description = ""

    // Try to get description from JSDoc comment: /** Description */
    const storyStart = match.index
    const beforeStory = content.substring(
      Math.max(0, storyStart - 200),
      storyStart
    )
    const jsDocMatch = beforeStory.match(/\/\*\*\s*([^*]+)\s*\*\/\s*$/)
    if (jsDocMatch) {
      description = jsDocMatch[1].trim()
    }

    // Try to extract code from render function
    let code = extractCodeFromRender(storyContent, componentName)

    // If no render function, try to generate from args
    if (!code) {
      const argsMatch = storyContent.match(/args:\s*\{([\s\S]+?)\n\s{2,4}\}/)
      if (argsMatch) {
        const argsCode = argsMatch[1].trim()
        code = generateCodeExample(componentName, storyName, argsCode)
      }
    }

    // Only add if we have valid code
    if (code && code.length > 0) {
      examples.push({
        title: storyName.replace(/([A-Z])/g, " $1").trim(),
        code,
        description,
      })
      count++
    }
  }

  return examples
}

/**
 * Extract code from render function in story
 */
function extractCodeFromRender(
  storyContent: string,
  componentName: string
): string | null {
  // Find render function: render: (args) => ( ... ) or render: (args) => { return ... }
  const renderMatch = storyContent.match(
    /render:\s*\([^)]*\)\s*=>\s*(\([\s\S]*)|(\{[\s\S]*)/
  )
  if (!renderMatch) return null

  let renderBody = renderMatch[1] || renderMatch[2]

  // If it's a function body with return, extract the return statement
  if (renderBody.includes("return")) {
    const returnMatch = renderBody.match(
      /return\s*\(?([\s\S]*?)\)?;?\s*\}\s*,?\s*$/
    )
    if (returnMatch) {
      renderBody = returnMatch[1]
    }
  }

  // Remove outer parentheses if present
  renderBody = renderBody.replace(/^\(\s*/, "").replace(/\s*\)\s*,?\s*$/, "")

  // Find the component in the render body - be more greedy to get the full component
  const componentStartRegex = new RegExp(`<${componentName}[\\s\\S]`)
  const startMatch = renderBody.match(componentStartRegex)

  if (!startMatch) return null

  const startIndex = startMatch.index!
  let componentCode = ""
  let depth = 0
  let inString = false
  let stringChar = ""
  let i = startIndex

  // Parse character by character to handle nested components
  while (i < renderBody.length) {
    const char = renderBody[i]
    const nextChar = renderBody[i + 1]

    componentCode += char

    // Track string boundaries
    if (
      (char === '"' || char === "'" || char === "`") &&
      renderBody[i - 1] !== "\\"
    ) {
      if (!inString) {
        inString = true
        stringChar = char
      } else if (char === stringChar) {
        inString = false
        stringChar = ""
      }
    }

    if (!inString) {
      // Track JSX depth
      if (char === "<" && nextChar !== "/") {
        // Check if it's a component opening tag, not a comparison
        const afterTag = renderBody.substring(i + 1, i + 50)
        if (afterTag.match(/^[A-Z\w]/)) {
          depth++
        }
      } else if (char === "/" && nextChar === ">") {
        // Self-closing tag
        depth--
        i++ // Skip the '>'
        componentCode += ">"
        if (depth === 0) break
      } else if (char === "<" && nextChar === "/") {
        // Closing tag
        depth--
        // Find the end of the closing tag
        const closeEnd = renderBody.indexOf(">", i)
        if (closeEnd !== -1) {
          componentCode += renderBody.substring(i + 1, closeEnd + 1)
          i = closeEnd
          if (depth === 0) break
        }
      }
    }

    i++
  }

  // Clean up the extracted code
  componentCode = cleanupJSXCode(componentCode, componentName)

  // Don't return if it's just spreading args
  if (
    componentCode.includes("{...args}") &&
    !componentCode.match(/\w+\s*=(?!\s*\{\.\.\.args)/)
  ) {
    return null
  }

  return componentCode || null
}

/**
 * Clean up and format JSX code
 */
function cleanupJSXCode(jsxCode: string, componentName: string): string {
  // Remove {...args} spread but preserve surrounding whitespace structure
  jsxCode = jsxCode.replace(/\s*\{\.\.\.args\}\s*/g, " ")

  // Fix cases where tag and attribute are concatenated (e.g., "<Selectsize=" -> "<Select size=")
  jsxCode = jsxCode.replace(
    new RegExp(`<${componentName}([a-z])`, "g"),
    `<${componentName} $1`
  )

  // Remove excessive indentation - find the minimum indentation and normalize
  const lines = jsxCode.split("\n")
  const nonEmptyLines = lines.filter((l) => l.trim().length > 0)

  if (nonEmptyLines.length === 0) return jsxCode

  // Find minimum indentation
  const minIndent = Math.min(
    ...nonEmptyLines.map((line) => {
      const match = line.match(/^(\s*)/)
      return match ? match[1].length : 0
    })
  )

  // Remove minimum indentation from all lines
  const normalizedLines = lines.map((line) => {
    if (line.trim().length === 0) return ""
    return line.substring(minIndent)
  })

  // Remove leading/trailing empty lines
  while (normalizedLines.length > 0 && normalizedLines[0].trim() === "") {
    normalizedLines.shift()
  }
  while (
    normalizedLines.length > 0 &&
    normalizedLines[normalizedLines.length - 1].trim() === ""
  ) {
    normalizedLines.pop()
  }

  return normalizedLines.join("\n").trim()
}

/**
 * Format JSX code nicely
 */
function formatJSX(jsxCode: string, componentName: string): string {
  // Check if it's a self-closing tag
  const isSelfClosing = jsxCode.endsWith("/>")

  // Extract props and content
  const tagMatch = jsxCode.match(new RegExp(`<${componentName}([^>]*?)(/?)>`))
  if (!tagMatch) return jsxCode

  const propsString = tagMatch[1].trim()
  const content = isSelfClosing
    ? null
    : jsxCode
        .match(
          new RegExp(`>${componentName}>([\\s\\S]*?)</${componentName}`)
        )?.[1]
        ?.trim()

  if (!propsString && !content) {
    return `<${componentName} />`
  }

  // Parse props
  const props: string[] = []
  const propRegex = /(\w+)=(?:\{([^}]+)\}|"([^"]+)")/g
  let propMatch: RegExpExecArray | null

  while ((propMatch = propRegex.exec(propsString)) !== null) {
    const propName = propMatch[1]
    const propValue = propMatch[2] || `"${propMatch[3]}"`

    // Format prop value
    let formattedValue = propValue
    if (propMatch[2]) {
      // It was in braces
      formattedValue = `{${propValue}}`
    } else {
      // It was a string
      formattedValue = `"${propMatch[3]}"`
    }

    props.push(`  ${propName}=${formattedValue}`)
  }

  // Build formatted code
  if (content) {
    if (props.length > 0) {
      return `<${componentName}\n${props.join("\n")}\n>\n  ${content}\n</${componentName}>`
    }
    return `<${componentName}>\n  ${content}\n</${componentName}>`
  } else if (props.length > 0) {
    return `<${componentName}\n${props.join("\n")}\n/>`
  }

  return `<${componentName} />`
}

/**
 * Generate code example from story args
 */
function generateCodeExample(
  componentName: string,
  storyName: string,
  argsCode: string
): string {
  if (!argsCode) {
    return `<${componentName}>\n  Content\n</${componentName}>`
  }

  let code = `<${componentName}`
  let children = ""
  const propLines: string[] = []

  // Parse args line by line
  const lines = argsCode.split(",").map((line) => line.trim())

  for (const line of lines) {
    if (!line) continue

    // Check for children
    if (line.includes("children:")) {
      const childMatch = line.match(/children:\s*["']([^"']+)["']/)
      if (childMatch) {
        children = childMatch[1]
        continue
      }
    }

    // Parse other props
    const propMatch = line.match(/(\w+):\s*(.+)/)
    if (propMatch) {
      const [, propName, propValue] = propMatch
      // Clean up the value
      let cleanValue = propValue.trim().replace(/[,;]+$/, "")

      // Format based on value type
      if (cleanValue === "true" || cleanValue === "false") {
        propLines.push(`  ${propName}={${cleanValue}}`)
      } else if (cleanValue.match(/^["']/)) {
        // String value
        propLines.push(`  ${propName}={${cleanValue}}`)
      } else if (cleanValue.match(/^\d+$/)) {
        // Number value
        propLines.push(`  ${propName}={${cleanValue}}`)
      } else if (cleanValue.match(/^</)) {
        // JSX element
        propLines.push(`  ${propName}={${cleanValue}}`)
      } else {
        // Default: wrap in braces
        propLines.push(`  ${propName}={${cleanValue}}`)
      }
    }
  }

  if (propLines.length > 0) {
    code += "\n" + propLines.join("\n")
  }

  if (children) {
    code += `>\n  ${children}\n</${componentName}>`
  } else {
    code += propLines.length > 0 ? "\n/>" : " />"
  }

  return code
}

/**
 * Extract guidelines from UsageGuidelines component
 */
function extractGuidelines(content: string, section: string): string[] {
  const guidelines: string[] = []

  // Find section in docs page - look for the header and UsageGuidelines
  const sectionRegex = new RegExp(
    `<h2[^>]*id=["']\\w*-${section.toLowerCase().replace(/\s+/g, "-")}["'][^>]*>[^<]*${section}[^<]*</h2>[\\s\\S]*?<UsageGuidelines\\s+guidelines=\\{\\[([\\s\\S]*?)\\]\\s*\\}`,
    "i"
  )
  const sectionMatch = content.match(sectionRegex)

  if (sectionMatch) {
    const guidelinesContent = sectionMatch[1]

    // Extract both simple strings and JSX fragments
    // Pattern 1: Simple string literals: "text"
    const simpleStringRegex = /"([^"]+)"/g
    let match: RegExpExecArray | null

    while ((match = simpleStringRegex.exec(guidelinesContent)) !== null) {
      // Check if this isn't part of a JSX fragment (not preceded by <>)
      const beforeMatch = guidelinesContent.substring(0, match.index)
      if (!beforeMatch.trimEnd().endsWith("<>")) {
        guidelines.push(match[1])
      }
    }

    // Pattern 2: JSX fragments: <> ... </>
    const jsxRegex = /<>\s*([\s\S]*?)\s*<\/>/g
    while ((match = jsxRegex.exec(guidelinesContent)) !== null) {
      let jsxContent = match[1]

      // Convert JSX to plain text
      // Replace <code>text</code> with `text`
      jsxContent = jsxContent.replace(/<code>([^<]+)<\/code>/g, "`$1`")

      // Remove {" "} and similar JSX spacing
      jsxContent = jsxContent.replace(/\{\s*["']\s*["']\s*\}/g, " ")

      // Clean up extra whitespace
      jsxContent = jsxContent.replace(/\s+/g, " ").trim()

      if (jsxContent) {
        guidelines.push(jsxContent)
      }
    }
  }

  // Filter out empty strings
  return guidelines.filter((g) => g.trim().length > 0)
}

/**
 * Generate markdown content for a component
 */
function generateComponentMarkdown(component: Component): string {
  let markdown = `# ${component.name}\n\n`
  markdown += `> ${component.description}\n\n`

  // Installation
  markdown += `## Installation\n\n`
  markdown += "```tsx\n"
  markdown += `import { ${component.name} } from "@apollo/ui"\n`
  markdown += "```\n\n"

  // Props
  if (component.props.length > 0) {
    markdown += `## Props\n\n`
    markdown += `| Prop | Type | Default | Description |\n`
    markdown += `|------|------|---------|-------------|\n`

    for (const prop of component.props) {
      // Escape pipe characters in type definitions for markdown table
      const escapedType = prop.type.replace(/\|/g, "\\|")
      markdown += `| ${prop.name} | \`${escapedType}\` | ${prop.defaultValue} | ${prop.description} |\n`
    }
    markdown += "\n"
  }

  // Examples
  if (component.examples.length > 0) {
    markdown += `## Examples\n\n`
    for (const example of component.examples) {
      markdown += `### ${example.title}\n\n`
      if (example.description) {
        markdown += `${example.description}\n\n`
      }
      markdown += "```tsx\n"
      markdown += example.code
      markdown += "\n```\n\n"
    }
  }

  // Usage Guidelines
  if (component.usageGuidelines.length > 0) {
    markdown += `## Best Practices\n\n`
    for (const guideline of component.usageGuidelines) {
      markdown += `- ${guideline}\n`
    }
    markdown += "\n"
  }

  // Accessibility
  if (component.accessibilityNotes.length > 0) {
    markdown += `## Accessibility\n\n`
    for (const note of component.accessibilityNotes) {
      markdown += `- ${note}\n`
    }
    markdown += "\n"
  }

  // Figma Link
  if (component.figmaUrl) {
    markdown += `## Design\n\n`
    markdown += `[View in Figma](${component.figmaUrl})\n\n`
  }

  return markdown
}

/**
 * Generate main llms.txt file
 */
function generateMainLlmsTxt(components: Component[]): string {
  let content = `# Apollo Design System\n\n`
  content += `> Apollo is a comprehensive React design system providing ${components.length}+ production-ready components with full accessibility support, theming capabilities, and TypeScript types. Built with Base UI and optimized for modern React applications.\n\n`

  content += `The Apollo Design System offers two packages:\n`
  content += `- **@apollo/ui** - Modern component library (recommended)\n`
  content += `- **@design-systems/apollo-ui** - Legacy version (maintenance mode)\n\n`

  content += `This documentation focuses on **@apollo/ui** components with interactive examples, comprehensive props documentation, and real-world usage patterns.\n\n`

  // Group components by category
  const grouped = components.reduce(
    (acc, comp) => {
      if (!acc[comp.category]) acc[comp.category] = []
      acc[comp.category].push(comp)
      return acc
    },
    {} as Record<string, Component[]>
  )

  // Sort categories
  const categoryOrder = [
    "Inputs",
    "Layout",
    "Navigation",
    "Data Display",
    "Feedback",
    "Utilities",
    "Theming",
  ]
  const sortedCategories = Object.keys(grouped).sort((a, b) => {
    const aIndex = categoryOrder.indexOf(a)
    const bIndex = categoryOrder.indexOf(b)
    if (aIndex === -1 && bIndex === -1) return a.localeCompare(b)
    if (aIndex === -1) return 1
    if (bIndex === -1) return -1
    return aIndex - bIndex
  })

  content += `## Components\n\n`

  for (const category of sortedCategories) {
    const comps = grouped[category]
    content += `### ${category}\n\n`

    for (const comp of comps.sort((a, b) => a.name.localeCompare(b.name))) {
      const slug = comp.name
        .toLowerCase()
        .replace(/([a-z])([A-Z])/g, "$1-$2")
        .toLowerCase()
      content += `- [${comp.name}](/llms/components/${comp.categorySlug}/${slug}.md): ${comp.description}\n`
    }
    content += "\n"
  }

  // Add optional section
  content += `## Optional\n\n`
  content += `- [Storybook Documentation](https://apollo-storybook.netlify.app): Interactive component explorer\n`
  content += `- [Contributing Guide](https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/CONTRIBUTING.md): Development setup and guidelines\n`

  return content
}

/**
 * Main execution
 */
async function main() {
  console.log("🚀 Generating llms.txt documentation...\n")

  const storiesDir = path.join(__dirname, "../src/stories/apollo-ui")
  const files = await fs.readdir(storiesDir)
  const storyFiles = files.filter((f) => f.endsWith(".stories.tsx"))

  console.log(`📦 Found ${storyFiles.length} story files\n`)

  // Extract metadata from all story files
  const components: Component[] = []
  for (const file of storyFiles) {
    try {
      console.log(`   Processing ${file}...`)
      const component = await extractComponentMetadata(file)
      if (component) {
        components.push(component)
        console.log(`   ✓ ${component.name} (${component.category})`)
      }
    } catch (error) {
      console.error(`   ✗ Error processing ${file}:`, error)
    }
  }

  console.log(`\n✅ Successfully processed ${components.length} components\n`)

  // Create output directories - use storybook-static for built output
  const outputDir = path.join(__dirname, "../storybook-static")
  const llmsDir = path.join(outputDir, "llms")
  const componentsDir = path.join(llmsDir, "components")

  await fs.mkdir(llmsDir, { recursive: true })
  await fs.mkdir(componentsDir, { recursive: true })

  // Generate main llms.txt
  console.log("📝 Generating /llms.txt...")
  const llmsTxt = generateMainLlmsTxt(components)
  await fs.writeFile(path.join(outputDir, "llms.txt"), llmsTxt)
  console.log("✅ Generated /llms.txt\n")

  // Generate component markdown files
  console.log("📝 Generating component markdown files...\n")

  for (const component of components) {
    try {
      const markdown = generateComponentMarkdown(component)

      // Create category directory
      const categoryDir = path.join(componentsDir, component.categorySlug)
      await fs.mkdir(categoryDir, { recursive: true })

      // Write markdown file
      const slug = component.name
        .toLowerCase()
        .replace(/([a-z])([A-Z])/g, "$1-$2")
        .toLowerCase()
      const filePath = path.join(categoryDir, `${slug}.md`)
      await fs.writeFile(filePath, markdown)

      console.log(
        `   ✓ ${component.name} → /llms/components/${component.categorySlug}/${slug}.md`
      )
    } catch (error) {
      console.error(`   ✗ Error generating ${component.name}:`, error)
    }
  }

  console.log("\n🎉 Done!\n")
  console.log("Generated files:")
  console.log(`   - storybook-static/llms.txt`)
  console.log(
    `   - storybook-static/llms/components/**/*.md (${components.length} files)\n`
  )
  console.log("Next steps:")
  console.log("   1. Review generated files")
  console.log("   2. Test with LLM tools")
  console.log("   3. Build Storybook to include in output")
  console.log("   4. Deploy with llms.txt included\n")
}

main().catch(console.error)
