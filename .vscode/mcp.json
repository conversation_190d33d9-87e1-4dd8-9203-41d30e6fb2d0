{
  "servers": {
    // "apollo-code-gen": {
    //   "type": "stdio",
    //   "command": "/Users/<USER>/Documents/workspace/apollo-design-system/apps/docs/node_modules/.bin/cj-apollo",
    //   "args": ["run", "mcp"]
    // },
    // "figma-dev-mode": {
    //   "type": "sse",
    //   "url": "http://localhost:3845/sse"
    // },
    "chrome-devtools": {
      "command": "npx",
      "args": [
        "chrome-devtools-mcp@latest"
        //  "--channel=canary",
        //     "--headless=true",
        //     "--isolated=true",
      ]
    },
    "figmaRemoteMcp": {
      "url": "https://mcp.figma.com/mcp",
      "type": "http"
    }
  }
}
