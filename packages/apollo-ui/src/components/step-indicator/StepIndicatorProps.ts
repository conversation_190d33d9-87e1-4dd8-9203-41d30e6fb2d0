import type { HTMLAttributes, ReactNode, ReactElement } from "react"
import type { BadgeProps } from "../badge"

type StepIndicatorItemState = "active" | "inactive" | "completed"

export type StepIndicatorItemProps = {
  state: StepIndicatorItemState
  label?: string
  icon?: ReactNode
  badge?: Pick<BadgeProps, "label" | "color" | "icon">
  title?: string
  description?: string
  renderLabel?: (props: { state?: StepIndicatorItemState; label?: string }) => ReactElement
  renderIndicator?: (props: { state?: StepIndicatorItemState; icon?: ReactNode }) => ReactElement
  renderContent?: (props: { state?: StepIndicatorItemState }) => ReactElement
}

export type StepIndicatorProps = {
  items: StepIndicatorItemProps[]
  direction?: "horizontal" | "vertical"
} & HTMLAttributes<HTMLDivElement>
