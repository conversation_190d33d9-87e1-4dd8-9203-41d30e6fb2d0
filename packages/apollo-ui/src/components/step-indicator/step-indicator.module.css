.stepIndicatorRoot {
  display: grid;
  align-items: start;
  grid-template-columns: auto auto 1fr;
  column-gap: var(--apl-alias-spacing-gap-gap5, 8px);
  row-gap: var(--apl-alias-spacing-gap-gap3, 4px);

  &[data-direction='horizontal'] {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: var(--apl-alias-spacing-gap-gap3, 4px);

    :global(.ApolloStepIndicator-item) {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: var(--apl-alias-spacing-gap-gap5, 8px);

      &:last-child {
        flex: 0;
      }
    }

    :global(.ApolloStepIndicator-indicator) {
      flex-direction: row;
      justify-content: flex-start;
      align-self: stretch;
    }

    :global(.ApolloStepIndicator-indicatorLine) {
      height: 1px;
      width: 100%;
      min-height: unset;
    }
  }
}

.stepItem {
  display: contents;
  color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

  &[data-inactive] {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
  }

  &:last-child .stepIndicatorLine {
    display: none;
  }
}

.stepIndicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  gap: var(--apl-alias-spacing-padding-padding3, 4px);
  background-color: transparent;
  transition: all 0.2s ease-in-out;
  color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);

  &[data-active] {
    color: var(--apl-alias-color-primary-primary, #016E2E);
  }
}

.stepIndicatorIcon {
  height: fit-content;
  display: flex;

  &[data-inactive] {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
  }
}

.stepIndicatorLine {
  background-color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
  width: 1px;
  height: 100%;
  min-height: 4px;
}

.stepContent {
  display: flex;
  flex-direction: column;
  gap: var(--apl-alias-spacing-gap-gap4, 6px);
}

.stepBadge {
  width: fit-content;
}