import {
  StepIndicator as CoreStepIndicator,
  StepIndicatorItem as CoreStepIndicatorItem,
} from "@apollo/core"
import classNames from "classnames"

import { Badge } from "../badge"
import { Pointer } from "../common/Pointer"
import { Typography } from "../typography"
import styles from "./step-indicator.module.css"
import type {
  StepIndicatorItemProps,
  StepIndicatorProps,
} from "./StepIndicatorProps"

export const StepIndicator = ({
  items,
  direction = "vertical",
  className,
  ...props
}: StepIndicatorProps) => {
  return (
    <CoreStepIndicator
      direction={direction}
      slots={{
        root: {
          className: classNames(
            "ApolloStepIndicator-root",
            styles.stepIndicatorRoot,
            className
          ),
          ...props,
        },
      }}
    >
      {items?.map((item: StepIndicatorItemProps, index) => (
        <CoreStepIndicatorItem
          key={`step-${index}-${item.title}`}
          state={item.state}
          slots={{
            item: {
              className: classNames(
                "ApolloStepIndicator-item",
                styles.stepItem
              ),
            },
            label: {
              className: "ApolloStepIndicator-label",
              render: item.renderLabel,
              children: item.label ? (
                <Typography level="bodyMediumEmphasized">
                  {item.label}
                </Typography>
              ) : null,
            },
            indicator: {
              className: classNames(
                "ApolloStepIndicator-indicator",
                styles.stepIndicator
              ),
              render: item.renderIndicator,
              children: (
                <>
                  <div
                    className={classNames(
                      "ApolloStepIndicator-indicatorIcon",
                      styles.stepIndicatorIcon
                    )}
                  >
                    {item.icon || <Pointer height={24} width={24} />}
                  </div>
                  <div
                    className={classNames(
                      "ApolloStepIndicator-indicatorLine",
                      styles.stepIndicatorLine
                    )}
                  />
                </>
              ),
            },
            content: {
              className: classNames(
                "ApolloStepIndicator-content",
                styles.stepContent
              ),
              render: item.renderContent,
              children: (
                <>
                  {item.badge && (
                    <Badge
                      label={item.badge.label}
                      color={item.state === 'inactive' ? "disabled" : item.badge.color}
                      icon={item.badge.icon}
                      className={styles.stepBadge}
                    />
                  )}
                  {item.title && (
                    <Typography level="bodyLarge">{item.title}</Typography>
                  )}
                  {item.description && (
                    <Typography level="labelMedium">
                      {item.description}
                    </Typography>
                  )}
                </>
              ),
            },
          }}
        />
      ))}
    </CoreStepIndicator>
  )
}
