import type { ReactElement, ReactEventHandler, <PERSON><PERSON><PERSON><PERSON>, Ref } from "react"

import type { FieldP<PERSON> } from "../field"

export type UploadBoxFileType<
  Multiple extends boolean = false,
  FileType = any,
> = Multiple extends true ? FileType[] : FileType
export type UploadBoxBaseFileType = {
  name: string
}
export type UploadBoxFile<
  Multiple extends boolean = false,
  FileType extends { name: string } = UploadBoxBaseFileType,
> = UploadBoxFileType<Multiple, FileType>

export type UploadBoxErrorState = {
  code: string
  message: string
}
export type UploadBoxState = {
  errors: UploadBoxErrorState[]
}

export type UploadBoxFileState = {
  key: string
  uploading?: boolean
  uploadDescription?: string
  uploadMessage?: string
  uploadProgress?: number
  errorMessage?: string
  helperTextMessage?: string
} & Pick<FieldProps, "helperTextDecorator">

export type UploadBoxProps<Multiple extends boolean> = {
  uploadButtonText?: string
  fileState?:
    | (Multiple extends true ? UploadBoxFileState[] : UploadB<PERSON>FileState)
    | null
  onCancelUpload?: (file: UploadBoxFile<false>, index: number) => void
  renderDescription?: (state: UploadBoxState) => ReactNode
  renderErrorMessage?: (state: UploadBoxState) => ReactNode
  value?: UploadBoxFile<Multiple, UploadBoxBaseFileType> | null
  multiple?: Multiple
  errorMessage?: string
  errorOnClose?: ReactEventHandler
  /**
   * @default ['jpg','png','svg']
   */
  allowedFilesExtension?: string[]
  /**
   * @default 5*1024*1024 // 5MB
   */
  maxFileSizeInBytes?: number
  /**
   * @default 6
   */
  fileLimit?: number
  alwaysShowUploadFileSection?: boolean
  onDelete?: Multiple extends true ? (fileIndex: number) => void : () => void
  onUpload?: (files: UploadBoxFileType<Multiple, File>) => void
  hideUploadCloseIcon?: (file: UploadBoxFileState) => boolean
} & Omit<FieldProps, "children" | "helperText">

export type UploadBoxComponent = {
  <Multiple extends boolean = false>(
    props: UploadBoxProps<Multiple>,
    ref?: Ref<HTMLDivElement>
  ): ReactElement
  displayName?: string
}
