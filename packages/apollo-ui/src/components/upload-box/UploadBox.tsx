"use client"

import {
  forwardRef,
  useCallback,
  useState,
  type Change<PERSON>vent<PERSON><PERSON><PERSON>,
  type ForwardedRef,
} from "react"
import classNames from "classnames"

import { Alert } from "../alert"
import { Button } from "../button"
import { Upload } from "../common/Upload"
import { Field } from "../field"
import { Typography } from "../typography"
import styles from "./uploadBox.module.css"
import type {
  UploadBoxComponent,
  UploadBoxErrorState,
  UploadBoxFile,
  UploadBoxFileState,
  UploadBoxFileType,
  UploadBoxProps,
} from "./UploadBoxProps"
import UploadedFileItem from "./UploadedFileItem"
import { validateFileByExtension } from "./utils"

const UploadBox = forwardRef(function UploadBoxRoot<
  Multiple extends boolean,
  File,
>(
  {
    value,
    multiple,
    fileState,
    onUpload,
    renderDescription,
    renderErrorMessage,
    errorMessage,
    errorOnClose,
    disabled,
    onDelete,
    onCancelUpload,
    fileLimit = 6,
    maxFileSizeInBytes = 5 * 1024 * 1024,
    allowedFilesExtension = ["jpg", "png", "svg"],
    label,
    required,
    fullWidth,
    uploadButtonText = "Upload File",
    alwaysShowUploadFileSection = false,
    hideUploadCloseIcon,
    ...fieldProps
  }: UploadBoxProps<Multiple>,
  ref: ForwardedRef<HTMLDivElement>
) {
  const [internalErrorMessages, setInternalErrorMessages] = useState<
    UploadBoxErrorState[]
  >([])

  const maxFileSizeInMB = maxFileSizeInBytes
    ? Number(maxFileSizeInBytes / (1024 * 1024)).toLocaleString("en-US", {
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      })
    : 0

  const acceptedFiles = allowedFilesExtension
    ?.map((ext) => `.${ext}`)
    ?.join(", ")

  const fileCount = multiple ? (value as UploadBoxFile<true>)?.length : 0
  const uploadedFileCount = multiple ? fileCount : 0
  const isShowErrorAlert = errorMessage || internalErrorMessages.length > 0
  const isUploadButtonDisabled =
    disabled || (multiple ? fileCount >= fileLimit : false)

  const handleUploadFiles: ChangeEventHandler<HTMLInputElement> = useCallback(
    (event) => {
      if (event.target.files) {
        const fileList = event.target.files
        let isError = false
        const validFiles: File[] = []
        if (multiple) {
          const currentUploadFilesCount =
            (value as UploadBoxFile<true>)?.length ?? 0
          if (fileList.length + currentUploadFilesCount > fileLimit) {
            isError = true
            setInternalErrorMessages([
              {
                code: "EXCEED_FILE_LIMIT",
                message: `The maximum file upload is ${fileLimit} files`,
              },
            ])
            return
          }
        }
        for (let fileIndex = 0; fileIndex < fileList.length; fileIndex++) {
          const file = fileList.item(fileIndex)
          if (file) {
            const { errorMessage } = validateFileByExtension({
              file,
              allowedExtensions: allowedFilesExtension,
              maxSizeInBytes: maxFileSizeInBytes ?? 0,
            })
            if (errorMessage) {
              setInternalErrorMessages(errorMessage)
              isError = true
              break
            }

            validFiles.push(file as File)
          }
        }
        if (!isError) {
          setInternalErrorMessages([])
          const resultFile = multiple ? validFiles : validFiles[0]
          onUpload?.(resultFile as UploadBoxFileType<Multiple>)
        }
      }
    },
    [
      allowedFilesExtension,
      fileLimit,
      maxFileSizeInBytes,
      multiple,
      onUpload,
      value,
    ]
  )

  // Prepare the field error message
  const errorText = isShowErrorAlert
    ? (errorMessage ??
      internalErrorMessages?.map((error) => error.message)?.join(","))
    : undefined

  const handleAlertErrorClose = errorMessage
    ? (errorOnClose ?? undefined)
    : internalErrorMessages
      ? () => setInternalErrorMessages([])
      : undefined

  return (
    <Field
      ref={ref}
      {...fieldProps}
      label={label}
      error={!!errorText}
      required={required}
      fullWidth={fullWidth}
      disabled={disabled}
      className={`${styles.formControl} ${fieldProps?.className || ""}`}
    >
      <div
        className={classNames(styles.uploadBox, {
          [styles.uploadBoxMultipleWrapper]:
            multiple || alwaysShowUploadFileSection,
          [styles.fullWidth]: fullWidth,
        })}
      >
        {(multiple || alwaysShowUploadFileSection) && isShowErrorAlert
          ? (renderErrorMessage?.({ errors: internalErrorMessages }) ?? (
              <Alert
                type="error"
                description={
                  errorMessage ??
                  internalErrorMessages
                    ?.map((error) => error.message)
                    ?.join(",")
                }
                fullWidth
                onClose={handleAlertErrorClose}
              />
            ))
          : null}
        {multiple || (!multiple && !value) || alwaysShowUploadFileSection ? (
          <div
            className={classNames(styles.uploadSection, {
              [styles.uploadSectionDisabled]: isUploadButtonDisabled,
              [styles.fullWidth]: fullWidth || alwaysShowUploadFileSection,
            })}
          >
            {!multiple && !value && !alwaysShowUploadFileSection && isShowErrorAlert
              ? (renderErrorMessage?.({ errors: internalErrorMessages }) ?? (
                  <Alert
                    type="error"
                    description={
                      errorMessage ??
                      internalErrorMessages
                        ?.map((error) => error.message)
                        ?.join(",")
                    }
                    fullWidth
                    onClose={handleAlertErrorClose}
                  />
                ))
              : null}
            <div className={styles.fileConditionContainer}>
              {renderDescription?.({ errors: internalErrorMessages }) ?? (
                <ul className={styles.fileConditionList}>
                  <li>
                    <Typography level="bodyMedium">
                      File support {acceptedFiles} only
                    </Typography>
                  </li>
                  {maxFileSizeInBytes ? (
                    <li>
                      <Typography level="bodyMedium">
                        Size not more than {maxFileSizeInMB} MB
                      </Typography>
                    </li>
                  ) : null}
                </ul>
              )}

              {isUploadButtonDisabled ? (
                <Button
                  className={styles.uploadButton}
                  disabled={true}
                  startDecorator={<Upload width={24} height={24} />}
                  tabIndex={-1}
                  variant="outline"
                >
                  {uploadButtonText}
                </Button>
              ) : (
                <label className={styles.uploadButton}>
                  <Button
                    startDecorator={<Upload width={24} height={24} />}
                    tabIndex={-1}
                    variant="outline"
                    onClick={(e) => {
                      e.preventDefault()
                      // Trigger the file input click programmatically
                      const fileInput = e.currentTarget
                        .nextElementSibling as HTMLInputElement
                      fileInput?.click()
                    }}
                  >
                    {uploadButtonText}
                  </Button>
                  <input
                    accept={acceptedFiles}
                    style={{ display: "none" }}
                    multiple={multiple}
                    onChange={handleUploadFiles}
                    type="file"
                    value=""
                  />
                </label>
              )}
            </div>
          </div>
        ) : null}
        {!multiple && value ? (
          <UploadedFileItem
            className={styles.uploadedSingleModeFileItem}
            name={(value as UploadBoxFile<false>)?.name ?? "-"}
            onCancelUpload={
              onCancelUpload
                ? () => {
                    if (value) {
                      onCancelUpload?.(value as UploadBoxFile<false>, 0)
                    }
                  }
                : undefined
            }
            onDelete={onDelete ? (onDelete as () => void) : undefined}
            uploading={(fileState as UploadBoxFileState)?.uploading}
            errorMessage={(fileState as UploadBoxFileState)?.errorMessage}
            uploadDescription={
              (fileState as UploadBoxFileState)?.uploadDescription
            }
            uploadMessage={(fileState as UploadBoxFileState)?.uploadMessage}
            helperTextMessage={
              (fileState as UploadBoxFileState)?.helperTextMessage
            }
            helperTextDecorator={
              (fileState as UploadBoxFileState)?.helperTextDecorator
            }
            uploadProgress={(fileState as UploadBoxFileState)?.uploadProgress}
            disabled={disabled}
            hideUploadCloseIcon={
              hideUploadCloseIcon
                ? () => hideUploadCloseIcon(fileState as UploadBoxFileState)
                : undefined
            }
          />
        ) : null}
        {multiple ? (
          <div className={styles.uploadedFileList}>
            <Typography className={styles.uploadedFilesCount} level="bodyLarge">
              {uploadedFileCount > 0
                ? `(${uploadedFileCount} Files uploaded)`
                : "(No file upload)"}
            </Typography>
            {(value as UploadBoxFile<true>)?.length > 0 ? (
              <div className={styles.uploadedMultipleModeFileList}>
                {(value as UploadBoxFile<true>)?.map((file, index) => (
                  <UploadedFileItem
                    className={styles.uploadedMultipleModeFileItem}
                    key={`${file.name}-${index}`}
                    name={file.name}
                    onCancelUpload={
                      onCancelUpload
                        ? () => {
                            onCancelUpload?.(file, index)
                          }
                        : undefined
                    }
                    onDelete={onDelete ? () => onDelete?.(index) : undefined}
                    uploading={
                      (fileState as UploadBoxFileState[])?.[index]?.uploading
                    }
                    errorMessage={
                      (fileState as UploadBoxFileState[])?.[index]?.errorMessage
                    }
                    uploadDescription={
                      (fileState as UploadBoxFileState[])?.[index]
                        ?.uploadDescription
                    }
                    uploadMessage={
                      (fileState as UploadBoxFileState[])?.[index]
                        ?.uploadMessage
                    }
                    helperTextMessage={
                      (fileState as UploadBoxFileState[])?.[index]
                        ?.helperTextMessage
                    }
                    helperTextDecorator={
                      (fileState as UploadBoxFileState[])?.[index]
                        ?.helperTextDecorator
                    }
                    uploadProgress={
                      (fileState as UploadBoxFileState[])?.[index]
                        ?.uploadProgress
                    }
                    disabled={disabled}
                    hideUploadCloseIcon={
                      hideUploadCloseIcon
                        ? () =>
                            hideUploadCloseIcon(
                              (fileState as UploadBoxFileState[])?.[index]
                            )
                        : undefined
                    }
                  />
                ))}
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
    </Field>
  )
}) as UploadBoxComponent

UploadBox.displayName = "UploadBox"

export default UploadBox
