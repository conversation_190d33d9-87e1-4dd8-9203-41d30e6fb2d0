import type { CSSProperties, HTMLProps, ReactElement, ReactNode } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"

import type { InputProps } from "../input"

/**
 * Option type for Combobox
 */
export type ComboboxOption = {
  label: string
  id: string
  disabled?: boolean

  render?: (
    props: HTMLProps<HTMLElement>,
    state: {
      selected: boolean
      disabled: boolean
    }
  ) => ReactElement
}

export type ComboboxGroup = {
  label: string
  id: string
  items: ComboboxOption[]
  disabled?: boolean
}

/**
 * Helper type to determine the value type based on the multiple prop
 */
export type ComboboxValueType<
  ItemValue,
  Multiple extends boolean | undefined,
> = Multiple extends true ? ItemValue[] : ItemValue

export type ComboboxProps<
  ItemValue = string,
  Multiple extends boolean | undefined = false,
> = {
  ref?: React.RefObject<HTMLDivElement>
  /**
   * Whether multiple items can be selected.
   * @default false
   */
  multiple?: Multiple
  /**
   * Array of options or groups to display in the combobox
   */
  options?: (ComboboxOption | ComboboxGroup)[]

  onValueChange?: (value: ComboboxValueType<ItemValue, Multiple> | null) => void
  size?: "medium" | "small"
  /**
   * Slot props for customizing internal components
   */
  slotProps?: ComboboxSlotProps
  /**
   * When items' ids are not sufficient, converts the id to a string label for display.
   */
  itemToStringLabel?: (itemId: string) => string
  /**
   * Show select all option in the menu (only for multiple mode)
   * @default false
   */
  showSelectAll?: boolean
  /**
   * Text for the select all option
   * @default "Select All"
   */
  selectAllText?: string
  /**
   * Show select all checkbox in each group label (only for multiple mode with grouped options)
   * @default false
   */
  showGroupSelectAll?: boolean
  /**
   * Type of option display in the menu (only for multiple mode)
   * @default "checkbox"
   */
  optionType?: "checkbox" | "text"
  noOptionsComponent?: ReactNode
  /**
   * Whether the combobox is loading
   * @default false
   */
  loading?: boolean
  /**
   * Custom loading component to display while loading
   */
  loadingComponent?: ReactNode
  /**
   * Whether more items are being loaded (for infinite scroll)
   * @default false
   */
  loadingMore?: boolean
  /**
   * Label for the load more trigger
   */
  loadMoreLabel?: ReactNode
  /**
   * Whether there are more items to load
   * @default false
   */
  hasMore?: boolean
  onLoadMore?: () => void
  /**
   * Async search callback; called when user types in the input
   * Return a promise to coordinate loading state
   */
  onSearch?: (searchValue: string) => Promise<void> | void
  /**
   * Debounce delay in milliseconds for onSearch callback
   * @default 200
   */
  debounceMs?: number
  /**
   * Disable search functionality in the combobox
   * @default false
   */
  disableSearch?: boolean
  /**
   * Custom filter logic for filtering options based on search input
   */
  filterLogic?: (
    options: ComboboxOption[],
    search: string,
    itemToStringLabel:  | ((itemValue: ItemValue) => string) | undefined
  ) => ComboboxOption[]
  className?: string
  style?: CSSProperties
} & Pick<
  InputProps,
  | "placeholder"
  | "label"
  | "fullWidth"
  | "disabled"
  | "helperText"
  | "error"
  | "required"
  | "labelDecorator"
  | "helperTextDecorator"
> &
  Omit<
    BaseCombobox.Root.Props<string, string, Multiple>,
    "onValueChange" | "children" | "items" | "filter"
  >

/**
 * Slot props for customizing Combobox sub-components
 */
export type ComboboxSlotProps = {
  container?: {
    className?: string
  }
  positioner?: {
    className?: string
  }
  popup?: {
    className?: string
  }
  optionList?: {
    className?: string
    children?: BaseCombobox.List.Props["children"]
    render?: (
      props: HTMLProps<HTMLDivElement>,
      state: BaseCombobox.List.State
    ) => ReactElement
  }
  option?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement> & { optionData: ComboboxOption },
      state: {
        selected: boolean
        disabled: boolean
      } & BaseCombobox.Item.State
    ) => ReactNode
  }
  noOptions?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement>,
      state: BaseCombobox.Empty.State
    ) => ReactElement
  }
  chips?: {
    className?: string
  }
  chip?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLDivElement> & { optionData: ComboboxOption },
      state: BaseCombobox.Chip.State
    ) => ReactNode
  }
  input?: {
    className?: string
    render?: (
      props: HTMLProps<HTMLInputElement>,
      state: BaseCombobox.Input.State
    ) => ReactElement
  }
  loading?: {
    className?: string
    render?: ReactElement | ((
      props: HTMLProps<HTMLDivElement>,
      state: BaseCombobox.Status.State
    ) => ReactElement)
  }
  loadingMore?: {
    className?: string
    render?: ReactElement | ((
      props: HTMLProps<HTMLDivElement>,
      state: BaseCombobox.Status.State
    ) => ReactElement)
  }
}
