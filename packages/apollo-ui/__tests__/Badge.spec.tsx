import { render } from "@testing-library/react"
import { describe, expect, test } from "vitest"

import { Badge } from "../src/components/badge"

const getComponent = (props = {}) => <Badge label="Test Badge" {...props} />

describe("Badge", () => {
  test("renders with default props", () => {
    const { container } = render(getComponent())
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveTextContent("Test Badge")
  })

  test("renders with default color", () => {
    const { container } = render(getComponent({ color: "default" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeDefault/)
  })

  test("renders with process color", () => {
    const { container } = render(getComponent({ color: "process" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeProcess/)
  })

  test("renders with success color", () => {
    const { container } = render(getComponent({ color: "success" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeSuccess/)
  })

  test("renders with warning color", () => {
    const { container } = render(getComponent({ color: "warning" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeWarning/)
  })

  test("renders with error color", () => {
    const { container } = render(getComponent({ color: "error" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeError/)
  })

  test("renders with disabled color", () => {
    const { container } = render(getComponent({ color: "disabled" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/badgeDisabled/)
  })

  test("renders with icon", () => {
    const icon = <span data-testid="test-icon">Icon</span>
    const { container, getByTestId } = render(getComponent({ icon }))
    const iconElement = getByTestId("test-icon")
    const iconWrapper = container.querySelector(".ApolloBadge-icon")
    expect(iconElement).toBeInTheDocument()
    expect(iconWrapper).toBeInTheDocument()
  })

  test("renders with children as overlay", () => {
    const children = <div data-testid="badge-child">Child content</div>
    const { container, getByTestId } = render(getComponent({ children }))
    const child = getByTestId("badge-child")
    const wrapper = container.querySelector(".ApolloBadge-wrapper")
    const badge = container.querySelector(".ApolloBadge-root")

    expect(child).toBeInTheDocument()
    expect(wrapper).toBeInTheDocument()
    expect(badge).toBeInTheDocument()
    expect(badge?.className).toMatch(/badgeIndicator/)
  })

  test("applies custom className", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge?.className).toMatch(/custom-class/)
  })

  test("renders label correctly", () => {
    const { container } = render(getComponent({ label: "Custom Label" }))
    const badge = container.querySelector(".ApolloBadge-root")
    expect(badge).toHaveTextContent("Custom Label")
  })
})
