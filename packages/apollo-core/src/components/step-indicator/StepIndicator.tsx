import { useSlotProps } from "../../hooks"
import type {
  StepIndicatorContentSlotProps,
  StepIndicatorIndicatorSlotProps,
  StepIndicatorItemProps,
  StepIndicatorItemSlotProps,
  StepIndicatorLabelSlotProps,
  StepIndicatorProps,
  StepIndicatorRootSlotProps,
} from "./StepIndicatorProps"

const COMPONENT_NAME = "StepIndicator"

export function StepIndicatorItem({ state, slots }: StepIndicatorItemProps) {
  const { Component: Item } = useSlotProps<StepIndicatorItemSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "item",
    props: { state, [`data-${state}`]: ''},
    slotProps: slots?.item,
    render: ({ className, children, ...props }) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  })

  const { Component: Label } = useSlotProps<StepIndicatorLabelSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "label",
    props: { state, [`data-${state}`]: '' },
    slotProps: slots?.label,
    render: ({ className, children, ...props }) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  })

  const { Component: Indicator } =
    useSlotProps<StepIndicatorIndicatorSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "indicator",
      props: { state, [`data-${state}`]: '' },
      slotProps: slots?.indicator,
      render: ({ className, children, ...props }) => (
        <div className={className} {...props}>
          {children}
        </div>
      ),
    })

  const { Component: Content } = useSlotProps<StepIndicatorContentSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "content",
    props: { state, [`data-${state}`]: '' },
    slotProps: slots?.content,
    render: ({ className, children, ...props }) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
  })

  return (
    <Item>
      <Label />
      <Indicator />
      <Content />
    </Item>
  )
}

export function StepIndicator({
  direction = "vertical",
  slots,
  children,
}: StepIndicatorProps) {
  const { Component: Root } = useSlotProps<StepIndicatorRootSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "root",
    props: { direction },
    slotProps: slots.root,
    render: ({ className, children, ...props }) => (
      <div className={className} data-direction={direction} {...props}>
        {children}
      </div>
    ),
  })

  return <Root>{children}</Root>
}
