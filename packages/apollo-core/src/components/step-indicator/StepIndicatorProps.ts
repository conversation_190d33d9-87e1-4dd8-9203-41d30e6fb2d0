import type { HTMLAttributes, ReactNode } from "react"

import type { ComponentSlotProps } from "../../types"

export type StepIndicatorItemState = "active" | "inactive" | "completed"

export type StepIndicatorDirection = "horizontal" | "vertical"

// Slot Props
export type StepIndicatorRootSlotProps = HTMLAttributes<HTMLDivElement> & {
  direction?: StepIndicatorDirection
}

export type StepIndicatorItemSlotProps = HTMLAttributes<HTMLDivElement> & {
  state?: StepIndicatorItemState
}

export type StepIndicatorIndicatorSlotProps = HTMLAttributes<HTMLDivElement> & {
  state?: StepIndicatorItemState
  icon?: ReactNode
}

export type StepIndicatorLabelSlotProps = HTMLAttributes<HTMLDivElement> & {
  state?: StepIndicatorItemState
  label?: string
}

export type StepIndicatorContentSlotProps = HTMLAttributes<HTMLDivElement> & {
  state?: StepIndicatorItemState
}


export type StepIndicatorItemSlots = {
  item?: ComponentSlotProps<StepIndicatorItemSlotProps>
  indicator?: ComponentSlotProps<StepIndicatorIndicatorSlotProps>
  label?: ComponentSlotProps<StepIndicatorLabelSlotProps>
  content?: ComponentSlotProps<StepIndicatorContentSlotProps>
}

export type StepIndicatorSlots = {
  root?: ComponentSlotProps<StepIndicatorRootSlotProps>
  item?: StepIndicatorItemSlots
}

export type StepIndicatorItemProps = {
  state: StepIndicatorItemState
  slots?: StepIndicatorItemSlots
}

export type StepIndicatorProps = {
  direction?: StepIndicatorDirection
  slots: StepIndicatorSlots
  children?: ReactNode
}
